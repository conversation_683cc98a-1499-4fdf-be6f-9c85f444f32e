<%- include('partials/header') %>

<!-- 标签页面 -->
<section class="tags-section">
    <div class="container">
        <div class="section-header">
            <h1 class="section-title">
                <i class="fas fa-tags"></i>
                标签云
            </h1>
            <p class="section-subtitle">探索不同主题的文章标签</p>
        </div>
        
        <% if (tags && tags.length > 0) { %>
            <div class="tags-cloud">
                <% tags.forEach(tag => { %>
                    <a href="/articles/tag/<%= encodeURIComponent(tag.name) %>" 
                       class="tag-item" 
                       style="font-size: <%= Math.min(2, Math.max(0.8, tag.count / 5)) %>rem;">
                        #<%= tag.name %>
                        <span class="tag-count">(<%= tag.count %>)</span>
                    </a>
                <% }) %>
            </div>
        <% } else { %>
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <h3>暂无标签</h3>
                <p>还没有任何文章标签，快去发布一些文章吧！</p>
                <a href="/articles" class="btn btn-primary">
                    <i class="fas fa-book-open"></i>
                    浏览文章
                </a>
            </div>
        <% } %>
    </div>
</section>

<style>
.tags-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 60vh;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.section-title i {
    margin-right: 15px;
    color: #ffd700;
}

.section-subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    max-width: 600px;
    margin: 0 auto;
}

.tags-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
    align-items: center;
    padding: 40px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255,255,255,0.2);
}

.tag-item {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 1px solid rgba(255,255,255,0.3);
    backdrop-filter: blur(5px);
}

.tag-item:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    color: white;
    text-decoration: none;
}

.tag-count {
    margin-left: 8px;
    font-size: 0.85em;
    opacity: 0.8;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255,255,255,0.2);
}

.empty-icon {
    font-size: 4rem;
    color: rgba(255,255,255,0.6);
    margin-bottom: 30px;
}

.empty-state h3 {
    font-size: 2rem;
    color: white;
    margin-bottom: 15px;
}

.empty-state p {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.8);
    margin-bottom: 30px;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(45deg, #007AFF, #5856D6);
    color: white;
    box-shadow: 0 4px 15px rgba(0,122,255,0.3);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,122,255,0.4);
    color: white;
    text-decoration: none;
}

.btn i {
    margin-right: 8px;
}

@media (max-width: 768px) {
    .section-title {
        font-size: 2.5rem;
    }
    
    .tags-cloud {
        padding: 20px;
        gap: 10px;
    }
    
    .tag-item {
        font-size: 0.9rem;
        padding: 6px 12px;
    }
    
    .empty-state {
        padding: 60px 20px;
    }
    
    .empty-icon {
        font-size: 3rem;
    }
    
    .empty-state h3 {
        font-size: 1.5rem;
    }
}
</style>

<%- include('partials/footer') %>
