// 管理后台JavaScript功能
document.addEventListener('DOMContentLoaded', function() {
    // 初始化管理后台功能
    initAdminSidebar();
    initAdminAlerts();
    initAdminForms();
});

// 侧边栏功能
function initAdminSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('adminSidebar');
    const mainContent = document.querySelector('.admin-main');
    
    if (sidebarToggle && sidebar) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            if (mainContent) {
                mainContent.classList.toggle('sidebar-collapsed');
            }
        });
    }
    
    // 高亮当前页面菜单项
    const currentPath = window.location.pathname;
    const menuItems = document.querySelectorAll('.sidebar-menu a');
    
    menuItems.forEach(item => {
        if (item.getAttribute('href') === currentPath) {
            item.classList.add('active');
        }
    });
}

// Alert自动关闭
function initAdminAlerts() {
    const alerts = document.querySelectorAll('.alert');
    
    alerts.forEach(alert => {
        // 添加关闭按钮
        const closeBtn = document.createElement('button');
        closeBtn.className = 'alert-close';
        closeBtn.innerHTML = '<i class="fas fa-times"></i>';
        closeBtn.addEventListener('click', function() {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        });
        alert.appendChild(closeBtn);
        
        // 自动关闭
        setTimeout(() => {
            if (alert.parentNode) {
                alert.style.opacity = '0';
                setTimeout(() => {
                    alert.remove();
                }, 300);
            }
        }, 5000);
    });
}

// 表单功能
function initAdminForms() {
    // 表单验证
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('error');
                    
                    // 移除错误样式
                    field.addEventListener('input', function() {
                        this.classList.remove('error');
                    });
                } else {
                    field.classList.remove('error');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('请填写所有必填字段', 'error');
            }
        });
    });
    
    // 文件上传预览
    const fileInputs = document.querySelectorAll('input[type="file"]');
    
    fileInputs.forEach(input => {
        input.addEventListener('change', function() {
            const file = this.files[0];
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    let preview = input.parentNode.querySelector('.file-preview');
                    if (!preview) {
                        preview = document.createElement('div');
                        preview.className = 'file-preview';
                        input.parentNode.appendChild(preview);
                    }
                    preview.innerHTML = `<img src="${e.target.result}" alt="预览" style="max-width: 200px; max-height: 200px;">`;
                };
                reader.readAsDataURL(file);
            }
        });
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${message}</span>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 关闭按钮
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', function() {
        notification.remove();
    });
    
    // 自动关闭
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// 确认对话框
function confirmAction(message, callback) {
    if (confirm(message)) {
        callback();
    }
}

// 数据表格功能
function initDataTable() {
    const tables = document.querySelectorAll('.data-table');
    
    tables.forEach(table => {
        // 排序功能
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                const column = this.dataset.sort;
                const rows = Array.from(table.querySelectorAll('tbody tr'));
                const isAsc = this.classList.contains('sort-asc');
                
                // 清除其他列的排序样式
                headers.forEach(h => h.classList.remove('sort-asc', 'sort-desc'));
                
                // 排序
                rows.sort((a, b) => {
                    const aVal = a.querySelector(`[data-column="${column}"]`).textContent.trim();
                    const bVal = b.querySelector(`[data-column="${column}"]`).textContent.trim();
                    
                    if (isAsc) {
                        return bVal.localeCompare(aVal);
                    } else {
                        return aVal.localeCompare(bVal);
                    }
                });
                
                // 更新表格
                const tbody = table.querySelector('tbody');
                rows.forEach(row => tbody.appendChild(row));
                
                // 更新排序样式
                this.classList.add(isAsc ? 'sort-desc' : 'sort-asc');
            });
        });
    });
}

// 批量操作
function initBatchActions() {
    const selectAll = document.querySelector('#selectAll');
    const checkboxes = document.querySelectorAll('.item-checkbox');
    const batchActions = document.querySelector('.batch-actions');
    
    if (selectAll && checkboxes.length > 0) {
        selectAll.addEventListener('change', function() {
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBatchActions();
        });
        
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBatchActions);
        });
    }
    
    function updateBatchActions() {
        const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
        if (batchActions) {
            batchActions.style.display = checkedCount > 0 ? 'block' : 'none';
            const countSpan = batchActions.querySelector('.selected-count');
            if (countSpan) {
                countSpan.textContent = checkedCount;
            }
        }
    }
}

// 富文本编辑器初始化
function initRichEditor() {
    const editorContainer = document.getElementById('editor');
    if (editorContainer && typeof Quill !== 'undefined') {
        const quill = new Quill('#editor', {
            theme: 'snow',
            modules: {
                toolbar: [
                    [{ 'header': [1, 2, 3, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'color': [] }, { 'background': [] }],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                    [{ 'align': [] }],
                    ['link', 'image', 'code-block'],
                    ['clean']
                ]
            }
        });
        
        // 同步内容到隐藏的textarea
        const textarea = document.getElementById('content');
        if (textarea) {
            quill.on('text-change', function() {
                textarea.value = quill.root.innerHTML;
            });
            
            // 初始化内容
            if (textarea.value) {
                quill.root.innerHTML = textarea.value;
            }
        }
    }
}

// 页面加载完成后初始化其他功能
window.addEventListener('load', function() {
    initDataTable();
    initBatchActions();
    initRichEditor();
});
