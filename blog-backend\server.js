const express = require('express');
const dotenv = require('dotenv');
const morgan = require('morgan');
const cors = require('cors');
// const helmet = require('helmet');
const path = require('path');
const config = require('./config/config');
const connectDB = require('./config/db');
const { errorHandler } = require('./middleware/errorHandler');

// Load env vars
dotenv.config();

// Initialize app
const app = express();

// Body parser
app.use(express.json());

// Enable CORS
app.use(cors());

// No helmet - pure Express

// Dev logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
}

// Serve static files from multiple directories
app.use(express.static(path.join(__dirname, '../')));
app.use('/blog-frontend-new', express.static(path.join(__dirname, '../blog-frontend-new')));
app.use(express.static(path.join(__dirname, '../blog-frontend-new')));

// Mount routes
app.use('/api/auth', require('./routes/authRoutes'));
app.use('/api/articles', require('./routes/articleRoutes'));
app.use('/api/categories', require('./routes/categoryRoutes'));
app.use('/api/analytics', require('./routes/analyticsRoutes'));
app.use('/api/search', require('./routes/search'));

// Custom error handler
app.use(errorHandler);

// Connect to database asynchronously
let dbConnection = null;
if (process.env.NODE_ENV !== 'test') {
  (async () => {
    dbConnection = await connectDB();
  })();
}

// Define server variable so it can be exported for testing
let server;

// Only start the server if file is run directly (not required/imported)
if (require.main === module) {
  const PORT = process.env.PORT || 5000;
  server = app.listen(PORT, () => {
    console.log(`Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (err, promise) => {
    console.log(`Error: ${err.message}`);
    // Close server & exit process
    server.close(() => process.exit(1));
  });
}

// Export for testing
module.exports = app; 