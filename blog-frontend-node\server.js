const express = require('express');
const path = require('path');
const cors = require('cors');
const session = require('express-session');
const flash = require('connect-flash');
const methodOverride = require('method-override');
const morgan = require('morgan');
require('dotenv').config();

const app = express();
const PORT = process.env.FRONTEND_PORT || 3000;
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000/api';

// 设置模板引擎
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// 中间件配置
app.use(morgan('dev'));
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(methodOverride('_method'));

// 静态文件服务
app.use(express.static(path.join(__dirname, 'public')));

// Session配置
app.use(session({
    secret: process.env.SESSION_SECRET || 'blog-secret-key',
    resave: false,
    saveUninitialized: false,
    cookie: { 
        secure: false, // 开发环境设为false
        maxAge: 24 * 60 * 60 * 1000 // 24小时
    }
}));

// Flash消息
app.use(flash());

// 全局变量中间件
app.use((req, res, next) => {
    res.locals.success_msg = req.flash('success_msg');
    res.locals.error_msg = req.flash('error_msg');
    res.locals.error = req.flash('error');
    res.locals.user = req.session.user || null;
    res.locals.API_BASE_URL = API_BASE_URL;
    next();
});

// 路由配置
const indexRoutes = require('./routes/index');
const articleRoutes = require('./routes/articles');
const adminRoutes = require('./routes/admin');

app.use('/', indexRoutes);
app.use('/articles', articleRoutes);
app.use('/admin', adminRoutes);

// 404错误处理
app.use((req, res) => {
    res.status(404).render('error', {
        title: '页面未找到',
        error: {
            status: 404,
            message: '抱歉，您访问的页面不存在'
        }
    });
});

// 错误处理中间件
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(err.status || 500).render('error', {
        title: '服务器错误',
        error: {
            status: err.status || 500,
            message: err.message || '服务器内部错误'
        }
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`前端服务器运行在 http://localhost:${PORT}`);
    console.log(`后端API地址: ${API_BASE_URL}`);
});

module.exports = app;
