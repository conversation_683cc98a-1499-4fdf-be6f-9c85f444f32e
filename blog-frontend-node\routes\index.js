const express = require('express');
const axios = require('axios');
const router = express.Router();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000/api';

// 创建axios实例
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000
});

// 首页路由
router.get('/', async (req, res) => {
    try {
        // 并行获取数据
        const [articlesRes, categoriesRes, statsRes, popularRes] = await Promise.allSettled([
            api.get('/articles?limit=6'),
            api.get('/categories'),
            api.get('/analytics/stats'),
            api.get('/analytics/popular?limit=5')
        ]);

        const articles = articlesRes.status === 'fulfilled' ? articlesRes.value.data.data : [];
        const categories = categoriesRes.status === 'fulfilled' ? categoriesRes.value.data.data : [];
        const stats = statsRes.status === 'fulfilled' ? statsRes.value.data.data : {};
        const popularArticles = popularRes.status === 'fulfilled' ? popularRes.value.data.data : [];

        res.render('index', {
            title: '现代博客 - 首页',
            articles,
            categories,
            stats,
            popularArticles,
            currentPage: 'home'
        });
    } catch (error) {
        console.error('首页数据加载错误:', error.message);
        res.render('index', {
            title: '现代博客 - 首页',
            articles: [],
            categories: [],
            stats: {},
            popularArticles: [],
            currentPage: 'home',
            error: '数据加载失败，请稍后重试'
        });
    }
});

// 分类页面
router.get('/categories', async (req, res) => {
    try {
        const [categoriesRes, distributionRes] = await Promise.allSettled([
            api.get('/categories'),
            api.get('/analytics/category-distribution')
        ]);

        const categories = categoriesRes.status === 'fulfilled' ? categoriesRes.value.data.data : [];
        const distribution = distributionRes.status === 'fulfilled' ? distributionRes.value.data.data : [];

        res.render('categories', {
            title: '文章分类',
            categories,
            distribution,
            currentPage: 'categories'
        });
    } catch (error) {
        console.error('分类页面数据加载错误:', error.message);
        res.render('categories', {
            title: '文章分类',
            categories: [],
            distribution: [],
            currentPage: 'categories',
            error: '数据加载失败，请稍后重试'
        });
    }
});

// 标签页面
router.get('/tags', async (req, res) => {
    try {
        const tagsRes = await api.get('/analytics/tags');
        const tags = tagsRes.data.data || [];

        res.render('tags', {
            title: '标签云',
            tags,
            currentPage: 'tags'
        });
    } catch (error) {
        console.error('标签页面数据加载错误:', error.message);
        res.render('tags', {
            title: '标签云',
            tags: [],
            currentPage: 'tags',
            error: '数据加载失败，请稍后重试'
        });
    }
});

// 搜索页面
router.get('/search', async (req, res) => {
    const { q, category, page = 1 } = req.query;
    
    try {
        let searchResults = [];
        let pagination = {};

        if (q) {
            const searchRes = await api.get('/search/articles', {
                params: { q, category, page, limit: 10 }
            });
            
            if (searchRes.data.success) {
                searchResults = searchRes.data.data;
                pagination = searchRes.data.pagination || {};
            }
        }

        // 获取分类列表用于过滤
        const categoriesRes = await api.get('/categories');
        const categories = categoriesRes.data.data || [];

        res.render('search', {
            title: '搜索结果',
            query: q || '',
            category: category || '',
            results: searchResults,
            categories,
            pagination,
            currentPage: 'search'
        });
    } catch (error) {
        console.error('搜索页面错误:', error.message);
        res.render('search', {
            title: '搜索结果',
            query: q || '',
            category: category || '',
            results: [],
            categories: [],
            pagination: {},
            currentPage: 'search',
            error: '搜索失败，请稍后重试'
        });
    }
});

// 关于页面
router.get('/about', async (req, res) => {
    try {
        const statsRes = await api.get('/analytics/stats');
        const stats = statsRes.data.data || {};

        res.render('about', {
            title: '关于我们',
            stats,
            currentPage: 'about'
        });
    } catch (error) {
        console.error('关于页面数据加载错误:', error.message);
        res.render('about', {
            title: '关于我们',
            stats: {},
            currentPage: 'about'
        });
    }
});

module.exports = router;
