<!-- 仪表板概览 -->
<div class="dashboard-overview">
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-file-alt"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><%= stats.totalArticles || 0 %></div>
                <div class="stat-label">文章总数</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-folder"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><%= stats.totalCategories || 0 %></div>
                <div class="stat-label">分类数量</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-comments"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><%= stats.totalComments || 0 %></div>
                <div class="stat-label">评论总数</div>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-edit"></i>
            </div>
            <div class="stat-content">
                <div class="stat-number"><%= stats.totalDrafts || 0 %></div>
                <div class="stat-label">草稿数量</div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="dashboard-charts">
    <div class="chart-row">
        <!-- 分类分布图 -->
        <div class="chart-card">
            <div class="chart-header">
                <h3 class="chart-title">分类分布</h3>
                <p class="chart-subtitle">各分类文章数量统计</p>
            </div>
            <div class="chart-container">
                <canvas id="categoryChart" width="400" height="200"></canvas>
            </div>
        </div>
        
        <!-- 发布趋势图 -->
        <div class="chart-card">
            <div class="chart-header">
                <h3 class="chart-title">发布趋势</h3>
                <p class="chart-subtitle">最近30天发布统计</p>
            </div>
            <div class="chart-container">
                <canvas id="trendChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 最新内容 -->
<div class="dashboard-content">
    <div class="content-row">
        <!-- 热门文章 -->
        <div class="content-card">
            <div class="content-header">
                <h3 class="content-title">热门文章</h3>
                <a href="/admin/articles" class="content-link">查看全部</a>
            </div>
            <div class="content-body">
                <% if (popularArticles && popularArticles.length > 0) { %>
                    <div class="article-list">
                        <% popularArticles.forEach((article, index) => { %>
                            <div class="article-item">
                                <div class="article-rank">#<%= index + 1 %></div>
                                <div class="article-info">
                                    <h4 class="article-title">
                                        <a href="/articles/<%= article._id %>" target="_blank">
                                            <%= article.title %>
                                        </a>
                                    </h4>
                                    <div class="article-meta">
                                        <span class="article-views">
                                            <i class="fas fa-eye"></i>
                                            <%= article.viewCount || 0 %>
                                        </span>
                                        <span class="article-date">
                                            <%= new Date(article.createdAt).toLocaleDateString('zh-CN') %>
                                        </span>
                                    </div>
                                </div>
                                <div class="article-actions">
                                    <a href="/admin/articles/<%= article._id %>/edit" class="btn-edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        <% }) %>
                    </div>
                <% } else { %>
                    <div class="empty-state">
                        <i class="fas fa-file-alt"></i>
                        <p>暂无热门文章</p>
                    </div>
                <% } %>
            </div>
        </div>
        
        <!-- 最新文章 -->
        <div class="content-card">
            <div class="content-header">
                <h3 class="content-title">最新文章</h3>
                <a href="/admin/articles/new" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i>
                    新建文章
                </a>
            </div>
            <div class="content-body">
                <% if (recentArticles && recentArticles.length > 0) { %>
                    <div class="article-list">
                        <% recentArticles.forEach(article => { %>
                            <div class="article-item">
                                <div class="article-status <%= article.status === 'published' ? 'published' : 'draft' %>">
                                    <i class="fas fa-<%= article.status === 'published' ? 'check' : 'edit' %>"></i>
                                </div>
                                <div class="article-info">
                                    <h4 class="article-title">
                                        <a href="/articles/<%= article._id %>" target="_blank">
                                            <%= article.title %>
                                        </a>
                                    </h4>
                                    <div class="article-meta">
                                        <span class="article-author">
                                            <i class="fas fa-user"></i>
                                            <%= article.author ? article.author.username : '匿名' %>
                                        </span>
                                        <span class="article-date">
                                            <%= new Date(article.createdAt).toLocaleDateString('zh-CN') %>
                                        </span>
                                    </div>
                                </div>
                                <div class="article-actions">
                                    <a href="/admin/articles/<%= article._id %>/edit" class="btn-edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        <% }) %>
                    </div>
                <% } else { %>
                    <div class="empty-state">
                        <i class="fas fa-file-alt"></i>
                        <p>暂无文章</p>
                        <a href="/admin/articles/new" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            创建第一篇文章
                        </a>
                    </div>
                <% } %>
            </div>
        </div>
    </div>
</div>

<!-- 页面特定的JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化图表
    initCharts();
});

function initCharts() {
    // 分类分布图
    const categoryCtx = document.getElementById('categoryChart');
    if (categoryCtx && typeof Chart !== 'undefined') {
        const categoryData = <%- JSON.stringify(distribution || []) %>;
        
        new Chart(categoryCtx, {
            type: 'doughnut',
            data: {
                labels: categoryData.map(item => item._id || '未分类'),
                datasets: [{
                    data: categoryData.map(item => item.count),
                    backgroundColor: [
                        '#007AFF',
                        '#5856D6',
                        '#34C759',
                        '#FF9500',
                        '#FF3B30',
                        '#AF52DE',
                        '#FF2D92',
                        '#5AC8FA'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }
    
    // 发布趋势图（示例数据）
    const trendCtx = document.getElementById('trendChart');
    if (trendCtx && typeof Chart !== 'undefined') {
        // 生成最近30天的示例数据
        const dates = [];
        const counts = [];
        for (let i = 29; i >= 0; i--) {
            const date = new Date();
            date.setDate(date.getDate() - i);
            dates.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
            counts.push(Math.floor(Math.random() * 5)); // 示例数据
        }
        
        new Chart(trendCtx, {
            type: 'line',
            data: {
                labels: dates,
                datasets: [{
                    label: '发布文章数',
                    data: counts,
                    borderColor: '#007AFF',
                    backgroundColor: 'rgba(0, 122, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            stepSize: 1
                        }
                    }
                }
            }
        });
    }
}
</script>
