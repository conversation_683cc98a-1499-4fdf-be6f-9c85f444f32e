/* ========== 管理后台样式 ========== */

/* 管理后台布局 */
.admin-body {
    display: flex;
    min-height: 100vh;
    background: var(--background-secondary);
    padding-top: 0;
}

/* 侧边栏 */
.admin-sidebar {
    width: 280px;
    background: var(--surface-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    flex-direction: column;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    transition: all var(--transition-normal);
}

.sidebar-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.sidebar-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    transition: all var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.sidebar-nav {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-radius: 0;
    margin: 0 var(--spacing-md);
    border-radius: var(--radius-medium);
}

.nav-link:hover,
.nav-link.active {
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
}

.nav-link i {
    width: 20px;
    text-align: center;
}

.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: var(--radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.user-details {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--font-size-sm);
}

.user-role {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.logout-form {
    width: 100%;
}

.logout-btn {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: none;
    border: 1px solid var(--text-tertiary);
    border-radius: var(--radius-medium);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
}

.logout-btn:hover {
    background: var(--error-color);
    border-color: var(--error-color);
    color: white;
}

/* 主内容区域 */
.admin-main {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.admin-header {
    background: var(--surface-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: var(--spacing-lg);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.mobile-sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    transition: all var(--transition-fast);
}

.mobile-sidebar-toggle:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.page-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
}

.header-right {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.admin-content {
    flex: 1;
    padding: var(--spacing-2xl);
}

/* 仪表板样式 */
.dashboard-overview {
    margin-bottom: var(--spacing-2xl);
}

.dashboard-charts {
    margin-bottom: var(--spacing-2xl);
}

.chart-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.chart-card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.chart-header {
    margin-bottom: var(--spacing-lg);
}

.chart-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.chart-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.chart-container {
    position: relative;
    height: 200px;
}

.dashboard-content {
    margin-bottom: var(--spacing-2xl);
}

.content-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.content-card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;
}

.content-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.content-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.content-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: all var(--transition-fast);
}

.content-link:hover {
    color: var(--primary-dark);
}

.content-body {
    padding: var(--spacing-lg);
}

.article-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.article-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-medium);
    transition: all var(--transition-fast);
}

.article-item:hover {
    background: var(--background-tertiary);
}

.article-rank {
    width: 30px;
    height: 30px;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.article-status {
    width: 30px;
    height: 30px;
    border-radius: var(--radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    flex-shrink: 0;
}

.article-status.published {
    background: var(--success-color);
    color: white;
}

.article-status.draft {
    background: var(--warning-color);
    color: white;
}

.article-info {
    flex: 1;
}

.article-title {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    font-weight: 500;
}

.article-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.article-title a:hover {
    color: var(--primary-color);
}

.article-meta {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.article-actions {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-edit {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.btn-edit:hover {
    background: var(--primary-dark);
    transform: scale(1.05);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .admin-sidebar {
        transform: translateX(-100%);
    }
    
    .admin-sidebar.mobile-active {
        transform: translateX(0);
    }
    
    .admin-main {
        margin-left: 0;
    }
    
    .mobile-sidebar-toggle {
        display: block;
    }
    
    .chart-row,
    .content-row {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .admin-content {
        padding: var(--spacing-lg);
    }
}
