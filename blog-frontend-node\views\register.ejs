<%- include('partials/header', { title: '用户注册', currentPage: 'register' }) %>

<div class="auth-page">
    <div class="container">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1 class="auth-title">创建账号</h1>
                    <p class="auth-subtitle">加入我们的博客社区</p>
                </div>
                
                <form class="auth-form" action="/auth/register" method="POST">
                    <div class="form-group">
                        <label for="username" class="form-label">用户名</label>
                        <input 
                            type="text" 
                            id="username" 
                            name="username" 
                            class="form-input" 
                            placeholder="请输入用户名"
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="email" class="form-label">邮箱地址</label>
                        <input 
                            type="email" 
                            id="email" 
                            name="email" 
                            class="form-input" 
                            placeholder="请输入邮箱地址"
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="请输入密码"
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="confirmPassword" class="form-label">确认密码</label>
                        <input 
                            type="password" 
                            id="confirmPassword" 
                            name="confirmPassword" 
                            class="form-input" 
                            placeholder="请再次输入密码"
                            required
                        >
                    </div>
                    
                    <!-- 管理员注册选项（仅用于创建第一个管理员） -->
                    <div class="form-group">
                        <label class="checkbox-label">
                            <input type="checkbox" id="isAdmin" name="isAdmin" value="true">
                            <span class="checkbox-custom"></span>
                            注册为管理员（仅限第一次使用）
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-user-plus"></i>
                        创建账号
                    </button>
                </form>
                
                <div class="auth-footer">
                    <p>已有账号？ <a href="/login" class="auth-link">立即登录</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<%- include('partials/footer') %>

<style>
.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: var(--spacing-xl) 0;
}

.auth-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.auth-card {
    background: var(--surface-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-large);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.auth-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.auth-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-input {
    padding: var(--spacing-md);
    border: 1px solid var(--text-tertiary);
    border-radius: var(--radius-medium);
    background: var(--background-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkbox-custom {
    width: 20px;
    height: 20px;
    border: 2px solid var(--text-tertiary);
    border-radius: var(--radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkbox-custom::after {
    content: '✓';
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.btn-full {
    width: 100%;
    justify-content: center;
    padding: var(--spacing-lg);
    font-size: var(--font-size-base);
    margin-top: var(--spacing-md);
}

.auth-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-footer p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-fast);
}

.auth-link:hover {
    color: var(--primary-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-card {
        padding: var(--spacing-2xl);
        margin: var(--spacing-lg);
    }
    
    .auth-title {
        font-size: var(--font-size-xl);
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card {
    animation: slideInUp 0.6s ease-out;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.auth-form');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    
    // 密码确认验证
    confirmPassword.addEventListener('input', function() {
        if (password.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('密码不匹配');
        } else {
            confirmPassword.setCustomValidity('');
        }
    });
    
    // 表单提交处理
    form.addEventListener('submit', function(e) {
        if (password.value !== confirmPassword.value) {
            e.preventDefault();
            showNotification('密码不匹配，请重新输入', 'error');
            return;
        }
    });
});
</script>
