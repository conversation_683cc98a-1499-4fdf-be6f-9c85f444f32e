<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - 管理后台</title>
    
    <!-- CSS样式 -->
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="/css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Rich text editor -->
    <script src="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/quill@1.3.7/dist/quill.snow.css" rel="stylesheet">
    
    <!-- Meta tags -->
    <meta name="description" content="现代博客系统管理后台">
    <meta name="robots" content="noindex, nofollow">
</head>
<body class="admin-body">
    <!-- 管理后台侧边栏 -->
    <aside class="admin-sidebar" id="adminSidebar">
        <div class="sidebar-header">
            <div class="sidebar-brand">
                <i class="fas fa-blog"></i>
                <span>管理后台</span>
            </div>
            <button class="sidebar-toggle" id="sidebarToggle">
                <i class="fas fa-bars"></i>
            </button>
        </div>
        
        <nav class="sidebar-nav">
            <ul class="nav-list">
                <li class="nav-item">
                    <a href="/admin" class="nav-link <%= title === '管理后台' ? 'active' : '' %>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>仪表板</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/articles" class="nav-link <%= title === '文章管理' ? 'active' : '' %>">
                        <i class="fas fa-file-alt"></i>
                        <span>文章管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/admin/articles/new" class="nav-link <%= title === '新建文章' ? 'active' : '' %>">
                        <i class="fas fa-plus"></i>
                        <span>新建文章</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/categories" class="nav-link">
                        <i class="fas fa-folder"></i>
                        <span>分类管理</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/" class="nav-link" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        <span>查看网站</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <div class="sidebar-footer">
            <div class="user-info">
                <div class="user-avatar">
                    <i class="fas fa-user"></i>
                </div>
                <div class="user-details">
                    <div class="user-name"><%= user ? user.username : '管理员' %></div>
                    <div class="user-role">管理员</div>
                </div>
            </div>
            <form action="/admin/logout" method="POST" class="logout-form">
                <button type="submit" class="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>退出登录</span>
                </button>
            </form>
        </div>
    </aside>

    <!-- 管理后台主内容区域 -->
    <main class="admin-main">
        <!-- 顶部栏 -->
        <header class="admin-header">
            <div class="header-left">
                <button class="mobile-sidebar-toggle" id="mobileSidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1 class="page-title"><%= title %></h1>
            </div>
            <div class="header-right">
                <div class="header-actions">
                    <a href="/" class="btn btn-outline" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        查看网站
                    </a>
                </div>
            </div>
        </header>

        <!-- Flash消息 -->
        <% if (success_msg && success_msg.length > 0) { %>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                <%= success_msg %>
            </div>
        <% } %>
        
        <% if (error_msg && error_msg.length > 0) { %>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-circle"></i>
                <%= error_msg %>
            </div>
        <% } %>
        
        <% if (typeof error !== 'undefined' && error) { %>
            <div class="alert alert-error">
                <i class="fas fa-exclamation-triangle"></i>
                <%= error %>
            </div>
        <% } %>

        <!-- 页面内容 -->
        <div class="admin-content">
            <%- body %>
        </div>
    </main>

    <!-- JavaScript -->
    <script src="/js/admin.js"></script>
    
    <!-- 页面特定的JavaScript -->
    <% if (typeof pageScript !== 'undefined') { %>
        <script src="/js/<%= pageScript %>"></script>
    <% } %>
</body>
</html>
