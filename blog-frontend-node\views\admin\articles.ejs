<!-- 页面头部 -->
<div class="page-header">
    <div class="header-left">
        <h1 class="page-title">文章管理</h1>
        <p class="page-subtitle">管理所有博客文章</p>
    </div>
    <div class="header-right">
        <a href="/admin/articles/new" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            新建文章
        </a>
    </div>
</div>

<!-- 筛选和搜索 -->
<div class="filters-section">
    <div class="filters-row">
        <div class="filter-group">
            <label for="statusFilter" class="filter-label">状态筛选</label>
            <select id="statusFilter" class="filter-select">
                <option value="">所有状态</option>
                <option value="published">已发布</option>
                <option value="draft">草稿</option>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="categoryFilter" class="filter-label">分类筛选</label>
            <select id="categoryFilter" class="filter-select">
                <option value="">所有分类</option>
                <% if (categories && categories.length > 0) { %>
                    <% categories.forEach(category => { %>
                        <option value="<%= category._id %>"><%= category.name %></option>
                    <% }) %>
                <% } %>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="searchInput" class="filter-label">搜索文章</label>
            <div class="search-input-group">
                <input type="text" id="searchInput" class="filter-input" placeholder="输入标题或内容...">
                <button type="button" class="search-btn">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 文章列表 -->
<div class="articles-table-container">
    <% if (articles && articles.length > 0) { %>
        <table class="articles-table">
            <thead>
                <tr>
                    <th class="sortable" data-sort="title">
                        标题
                        <i class="fas fa-sort"></i>
                    </th>
                    <th class="sortable" data-sort="status">
                        状态
                        <i class="fas fa-sort"></i>
                    </th>
                    <th class="sortable" data-sort="category">
                        分类
                        <i class="fas fa-sort"></i>
                    </th>
                    <th class="sortable" data-sort="author">
                        作者
                        <i class="fas fa-sort"></i>
                    </th>
                    <th class="sortable" data-sort="viewCount">
                        浏览量
                        <i class="fas fa-sort"></i>
                    </th>
                    <th class="sortable" data-sort="createdAt">
                        创建时间
                        <i class="fas fa-sort"></i>
                    </th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <% articles.forEach(article => { %>
                    <tr class="article-row" data-id="<%= article._id %>">
                        <td class="article-title-cell">
                            <div class="title-content">
                                <h4 class="article-title">
                                    <a href="/articles/<%= article._id %>" target="_blank">
                                        <%= article.title %>
                                    </a>
                                </h4>
                                <div class="article-excerpt">
                                    <%= article.content.substring(0, 100) %>...
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="status-badge status-<%= article.status %>">
                                <i class="fas fa-<%= article.status === 'published' ? 'check' : 'edit' %>"></i>
                                <%= article.status === 'published' ? '已发布' : '草稿' %>
                            </span>
                        </td>
                        <td>
                            <% if (article.category) { %>
                                <span class="category-tag">
                                    <i class="fas fa-folder"></i>
                                    <%= article.category.name %>
                                </span>
                            <% } else { %>
                                <span class="no-category">未分类</span>
                            <% } %>
                        </td>
                        <td>
                            <div class="author-info">
                                <i class="fas fa-user"></i>
                                <%= article.author ? article.author.username : '匿名' %>
                            </div>
                        </td>
                        <td>
                            <div class="view-count">
                                <i class="fas fa-eye"></i>
                                <%= article.viewCount || 0 %>
                            </div>
                        </td>
                        <td>
                            <div class="date-info">
                                <%= new Date(article.createdAt).toLocaleDateString('zh-CN') %>
                                <div class="time-info">
                                    <%= new Date(article.createdAt).toLocaleTimeString('zh-CN', { 
                                        hour: '2-digit', 
                                        minute: '2-digit' 
                                    }) %>
                                </div>
                            </div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="/articles/<%= article._id %>" target="_blank" class="btn-action btn-view" title="查看">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/admin/articles/<%= article._id %>/edit" class="btn-action btn-edit" title="编辑">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="btn-action btn-delete" data-id="<%= article._id %>" title="删除">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                <% }) %>
            </tbody>
        </table>
        
        <!-- 分页 -->
        <% if (pagination && pagination.totalPages > 1) { %>
        <div class="pagination-container">
            <div class="pagination-info">
                显示第 <%= (pagination.currentPage - 1) * pagination.limit + 1 %> - 
                <%= Math.min(pagination.currentPage * pagination.limit, pagination.total) %> 条，
                共 <%= pagination.total %> 条记录
            </div>
            <div class="pagination">
                <% if (pagination.currentPage > 1) { %>
                    <a href="?page=<%= pagination.currentPage - 1 %>" class="pagination-btn">
                        <i class="fas fa-chevron-left"></i>
                        上一页
                    </a>
                <% } %>
                
                <% for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) { %>
                    <% if (i === pagination.currentPage) { %>
                        <span class="pagination-btn current"><%= i %></span>
                    <% } else { %>
                        <a href="?page=<%= i %>" class="pagination-btn"><%= i %></a>
                    <% } %>
                <% } %>
                
                <% if (pagination.currentPage < pagination.totalPages) { %>
                    <a href="?page=<%= pagination.currentPage + 1 %>" class="pagination-btn">
                        下一页
                        <i class="fas fa-chevron-right"></i>
                    </a>
                <% } %>
            </div>
        </div>
        <% } %>
    <% } else { %>
        <div class="empty-state">
            <i class="fas fa-file-alt"></i>
            <h3>暂无文章</h3>
            <p>还没有创建任何文章，点击上方按钮开始创建第一篇文章。</p>
            <a href="/admin/articles/new" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                创建第一篇文章
            </a>
        </div>
    <% } %>
</div>

<!-- 删除确认模态框 -->
<div class="modal" id="deleteModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title">确认删除</h3>
            <button class="modal-close" onclick="closeDeleteModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <p>您确定要删除这篇文章吗？此操作不可撤销。</p>
        </div>
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeDeleteModal()">取消</button>
            <button class="btn btn-danger" id="confirmDeleteBtn">确认删除</button>
        </div>
    </div>
</div>

<style>
/* 页面头部 */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.page-subtitle {
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

/* 筛选区域 */
.filters-section {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filters-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.filter-label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-primary);
}

.filter-select,
.filter-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--text-tertiary);
    border-radius: var(--radius-medium);
    background: var(--background-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.filter-select:focus,
.filter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-input-group {
    display: flex;
    gap: var(--spacing-xs);
}

.search-input-group .filter-input {
    flex: 1;
}

.search-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.search-btn:hover {
    background: var(--primary-dark);
}

/* 文章表格 */
.articles-table-container {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    overflow: hidden;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.articles-table {
    width: 100%;
    border-collapse: collapse;
}

.articles-table th,
.articles-table td {
    padding: var(--spacing-md);
    text-align: left;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.articles-table th {
    background: var(--background-secondary);
    font-weight: 600;
    color: var(--text-primary);
    position: sticky;
    top: 0;
    z-index: 10;
}

.sortable {
    cursor: pointer;
    user-select: none;
    transition: all var(--transition-fast);
}

.sortable:hover {
    background: var(--background-tertiary);
}

.sortable i {
    margin-left: var(--spacing-xs);
    opacity: 0.5;
}

.article-row:hover {
    background: var(--background-secondary);
}

.article-title-cell {
    max-width: 300px;
}

.title-content .article-title {
    margin: 0 0 var(--spacing-xs) 0;
    font-size: var(--font-size-base);
    font-weight: 500;
}

.title-content .article-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.title-content .article-title a:hover {
    color: var(--primary-color);
}

.article-excerpt {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    line-height: 1.4;
}

.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-small);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.status-published {
    background: rgba(52, 199, 89, 0.1);
    color: var(--success-color);
}

.status-draft {
    background: rgba(255, 149, 0, 0.1);
    color: var(--warning-color);
}

.category-tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
    border-radius: var(--radius-small);
    font-size: var(--font-size-xs);
}

.no-category {
    color: var(--text-tertiary);
    font-size: var(--font-size-xs);
    font-style: italic;
}

.author-info,
.view-count {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.date-info {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.time-info {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
}

.action-buttons {
    display: flex;
    gap: var(--spacing-xs);
}

.btn-action {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: var(--radius-small);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    font-size: var(--font-size-sm);
}

.btn-view {
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
}

.btn-view:hover {
    background: var(--primary-color);
    color: white;
}

.btn-edit {
    background: rgba(255, 149, 0, 0.1);
    color: var(--warning-color);
}

.btn-edit:hover {
    background: var(--warning-color);
    color: white;
}

.btn-delete {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-color);
}

.btn-delete:hover {
    background: var(--error-color);
    color: white;
}

/* 分页 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    background: var(--background-secondary);
}

.pagination-info {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    max-width: 400px;
    width: 90%;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-title {
    margin: 0;
    color: var(--text-primary);
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--background-secondary);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--spacing-lg);
    color: var(--text-primary);
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .filters-row {
        grid-template-columns: 1fr;
    }
    
    .articles-table-container {
        overflow-x: auto;
    }
    
    .articles-table {
        min-width: 800px;
    }
    
    .pagination-container {
        flex-direction: column;
        gap: var(--spacing-md);
    }
}
</style>

<script>
// 删除文章功能
let deleteArticleId = null;

document.addEventListener('DOMContentLoaded', function() {
    // 删除按钮事件
    document.querySelectorAll('.btn-delete').forEach(btn => {
        btn.addEventListener('click', function() {
            deleteArticleId = this.dataset.id;
            document.getElementById('deleteModal').classList.add('active');
        });
    });
    
    // 确认删除
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        if (deleteArticleId) {
            deleteArticle(deleteArticleId);
        }
    });
    
    // 筛选功能
    document.getElementById('statusFilter').addEventListener('change', filterArticles);
    document.getElementById('categoryFilter').addEventListener('change', filterArticles);
    document.getElementById('searchInput').addEventListener('input', debounce(filterArticles, 300));
    
    // 排序功能
    document.querySelectorAll('.sortable').forEach(th => {
        th.addEventListener('click', function() {
            const sortBy = this.dataset.sort;
            sortArticles(sortBy);
        });
    });
});

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.remove('active');
    deleteArticleId = null;
}

async function deleteArticle(articleId) {
    try {
        const response = await fetch(`/admin/articles/${articleId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            // 移除表格行
            const row = document.querySelector(`[data-id="${articleId}"]`);
            if (row) {
                row.remove();
            }
            showNotification('文章删除成功', 'success');
        } else {
            showNotification('删除失败，请重试', 'error');
        }
    } catch (error) {
        console.error('删除文章失败:', error);
        showNotification('删除失败，请重试', 'error');
    }
    
    closeDeleteModal();
}

function filterArticles() {
    const status = document.getElementById('statusFilter').value;
    const category = document.getElementById('categoryFilter').value;
    const search = document.getElementById('searchInput').value.toLowerCase();
    
    const rows = document.querySelectorAll('.article-row');
    
    rows.forEach(row => {
        let show = true;
        
        // 状态筛选
        if (status) {
            const statusBadge = row.querySelector('.status-badge');
            if (!statusBadge.classList.contains(`status-${status}`)) {
                show = false;
            }
        }
        
        // 分类筛选
        if (category && show) {
            const categoryTag = row.querySelector('.category-tag');
            if (!categoryTag || !categoryTag.textContent.includes(category)) {
                show = false;
            }
        }
        
        // 搜索筛选
        if (search && show) {
            const title = row.querySelector('.article-title').textContent.toLowerCase();
            const excerpt = row.querySelector('.article-excerpt').textContent.toLowerCase();
            if (!title.includes(search) && !excerpt.includes(search)) {
                show = false;
            }
        }
        
        row.style.display = show ? '' : 'none';
    });
}

function sortArticles(sortBy) {
    // 这里可以实现客户端排序或发送请求到服务器
    console.log('排序依据:', sortBy);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
