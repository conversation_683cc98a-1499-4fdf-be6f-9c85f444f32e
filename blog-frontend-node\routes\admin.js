const express = require('express');
const axios = require('axios');
const router = express.Router();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000/api';

// 创建axios实例
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000
});

// 认证中间件
const requireAuth = (req, res, next) => {
    if (!req.session.user || req.session.user.role !== 'admin') {
        req.flash('error_msg', '请先登录管理员账户');
        return res.redirect('/admin/login');
    }
    next();
};

// 管理员登录页面
router.get('/login', (req, res) => {
    if (req.session.user && req.session.user.role === 'admin') {
        return res.redirect('/admin');
    }
    
    res.render('admin/login', {
        title: '管理员登录',
        layout: 'admin'
    });
});

// 处理登录
router.post('/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        const response = await api.post('/auth/login', {
            username,
            password
        });

        if (response.data.success) {
            const { user, token } = response.data.data;
            
            if (user.role !== 'admin') {
                req.flash('error_msg', '权限不足，需要管理员权限');
                return res.redirect('/admin/login');
            }

            // 保存用户信息到session
            req.session.user = user;
            req.session.token = token;
            
            req.flash('success_msg', '登录成功');
            res.redirect('/admin');
        } else {
            req.flash('error_msg', response.data.message || '登录失败');
            res.redirect('/admin/login');
        }
    } catch (error) {
        console.error('登录错误:', error.message);
        req.flash('error_msg', '登录失败，请检查用户名和密码');
        res.redirect('/admin/login');
    }
});

// 管理员退出
router.post('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('退出登录错误:', err);
        }
        res.redirect('/admin/login');
    });
});

// 管理后台首页（仪表板）
router.get('/', requireAuth, async (req, res) => {
    try {
        // 获取仪表板数据
        const [statsRes, distributionRes, popularRes, recentRes] = await Promise.allSettled([
            api.get('/analytics/stats', {
                headers: { Authorization: `Bearer ${req.session.token}` }
            }),
            api.get('/analytics/category-distribution'),
            api.get('/analytics/popular?limit=10'),
            api.get('/articles/all?limit=5', {
                headers: { Authorization: `Bearer ${req.session.token}` }
            })
        ]);

        const stats = statsRes.status === 'fulfilled' ? statsRes.value.data.data : {};
        const distribution = distributionRes.status === 'fulfilled' ? distributionRes.value.data.data : [];
        const popularArticles = popularRes.status === 'fulfilled' ? popularRes.value.data.data : [];
        const recentArticles = recentRes.status === 'fulfilled' ? recentRes.value.data.data : [];

        res.render('admin/dashboard', {
            title: '管理后台',
            stats,
            distribution,
            popularArticles,
            recentArticles,
            layout: 'admin'
        });
    } catch (error) {
        console.error('仪表板数据加载错误:', error.message);
        res.render('admin/dashboard', {
            title: '管理后台',
            stats: {},
            distribution: [],
            popularArticles: [],
            recentArticles: [],
            layout: 'admin',
            error: '数据加载失败，请稍后重试'
        });
    }
});

// 文章管理页面
router.get('/articles', requireAuth, async (req, res) => {
    try {
        const { page = 1, limit = 20 } = req.query;
        
        const response = await api.get('/articles/all', {
            params: { page, limit },
            headers: { Authorization: `Bearer ${req.session.token}` }
        });

        const articles = response.data.data || [];
        const pagination = response.data.pagination || {};

        res.render('admin/articles', {
            title: '文章管理',
            articles,
            pagination,
            layout: 'admin'
        });
    } catch (error) {
        console.error('文章管理页面错误:', error.message);
        res.render('admin/articles', {
            title: '文章管理',
            articles: [],
            pagination: {},
            layout: 'admin',
            error: '文章加载失败，请稍后重试'
        });
    }
});

// 新建文章页面
router.get('/articles/new', requireAuth, async (req, res) => {
    try {
        const categoriesRes = await api.get('/categories');
        const categories = categoriesRes.data.data || [];

        res.render('admin/article-form', {
            title: '新建文章',
            article: null,
            categories,
            layout: 'admin'
        });
    } catch (error) {
        console.error('新建文章页面错误:', error.message);
        res.render('admin/article-form', {
            title: '新建文章',
            article: null,
            categories: [],
            layout: 'admin',
            error: '页面加载失败，请稍后重试'
        });
    }
});

// 编辑文章页面
router.get('/articles/:id/edit', requireAuth, async (req, res) => {
    try {
        const { id } = req.params;
        
        const [articleRes, categoriesRes] = await Promise.allSettled([
            api.get(`/articles/${id}`),
            api.get('/categories')
        ]);

        if (articleRes.status === 'rejected') {
            req.flash('error_msg', '文章不存在');
            return res.redirect('/admin/articles');
        }

        const article = articleRes.value.data.data;
        const categories = categoriesRes.status === 'fulfilled' ? categoriesRes.value.data.data : [];

        res.render('admin/article-form', {
            title: '编辑文章',
            article,
            categories,
            layout: 'admin'
        });
    } catch (error) {
        console.error('编辑文章页面错误:', error.message);
        req.flash('error_msg', '页面加载失败');
        res.redirect('/admin/articles');
    }
});

module.exports = router;
