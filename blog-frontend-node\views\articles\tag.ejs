<%- include('../partials/header') %>

<!-- 标签文章页面 -->
<section class="tag-articles-section">
    <div class="container">
        <div class="section-header">
            <h1 class="section-title">
                <i class="fas fa-tag"></i>
                #<%= tag %>
            </h1>
            <p class="section-subtitle">
                浏览标签 "<%= tag %>" 下的所有文章
            </p>
        </div>
        
        <% if (articles && articles.length > 0) { %>
            <div class="articles-grid">
                <% articles.forEach(article => { %>
                    <article class="article-card">
                        <a href="/articles/<%= article._id %>" class="article-link">
                            <h3 class="article-title"><%= article.title %></h3>
                            <div class="article-meta">
                                <span class="article-author">
                                    <i class="fas fa-user"></i>
                                    <%= article.author ? article.author.username : '匿名' %>
                                </span>
                                <span class="article-date">
                                    <i class="fas fa-calendar"></i>
                                    <%= new Date(article.createdAt).toLocaleDateString('zh-CN') %>
                                </span>
                                <span class="article-views">
                                    <i class="fas fa-eye"></i>
                                    <%= article.viewCount || 0 %> 次浏览
                                </span>
                                <% if (article.category) { %>
                                <span class="article-category">
                                    <i class="fas fa-folder"></i>
                                    <%= article.category.name %>
                                </span>
                                <% } %>
                            </div>
                            <div class="article-excerpt">
                                <%= article.content.substring(0, 150) %>...
                            </div>
                            <% if (article.tags && article.tags.length > 0) { %>
                            <div class="article-tags">
                                <% article.tags.slice(0, 3).forEach(articleTag => { %>
                                    <span class="tag <%= articleTag === tag ? 'tag-current' : '' %>">
                                        #<%= articleTag %>
                                    </span>
                                <% }) %>
                            </div>
                            <% } %>
                        </a>
                    </article>
                <% }) %>
            </div>
            
            <!-- 分页 -->
            <% if (pagination && pagination.totalPages > 1) { %>
            <div class="pagination-wrapper">
                <nav class="pagination">
                    <% if (pagination.currentPage > 1) { %>
                        <a href="/articles/tag/<%= encodeURIComponent(tag) %>?page=<%= pagination.currentPage - 1 %>" class="pagination-btn">
                            <i class="fas fa-chevron-left"></i>
                            上一页
                        </a>
                    <% } %>
                    
                    <div class="pagination-info">
                        第 <%= pagination.currentPage %> 页，共 <%= pagination.totalPages %> 页
                    </div>
                    
                    <% if (pagination.currentPage < pagination.totalPages) { %>
                        <a href="/articles/tag/<%= encodeURIComponent(tag) %>?page=<%= pagination.currentPage + 1 %>" class="pagination-btn">
                            下一页
                            <i class="fas fa-chevron-right"></i>
                        </a>
                    <% } %>
                </nav>
            </div>
            <% } %>
        <% } else { %>
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-tag"></i>
                </div>
                <h3>该标签下暂无文章</h3>
                <p>标签 "<%= tag %>" 还没有任何文章，快去发布一些内容吧！</p>
                <div class="empty-actions">
                    <a href="/articles" class="btn btn-primary">
                        <i class="fas fa-book-open"></i>
                        浏览所有文章
                    </a>
                    <a href="/tags" class="btn btn-outline">
                        <i class="fas fa-tags"></i>
                        查看所有标签
                    </a>
                </div>
            </div>
        <% } %>
    </div>
</section>

<style>
.tag-articles-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 80vh;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.section-title i {
    margin-right: 15px;
    color: #ffd700;
}

.section-subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    max-width: 600px;
    margin: 0 auto;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 60px;
}

.article-card {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 30px;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.article-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    background: rgba(255,255,255,0.15);
}

.article-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.article-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    margin-bottom: 15px;
    line-height: 1.4;
}

.article-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.article-excerpt {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    margin-bottom: 15px;
}

.article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.tag-current {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255,215,0,0.3);
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    padding: 15px 30px;
    border-radius: 50px;
    border: 1px solid rgba(255,255,255,0.2);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.pagination-info {
    color: rgba(255,255,255,0.9);
    font-weight: 500;
}

.empty-state {
    text-align: center;
    padding: 80px 20px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    border: 1px solid rgba(255,255,255,0.2);
}

.empty-icon {
    font-size: 4rem;
    color: rgba(255,255,255,0.6);
    margin-bottom: 30px;
}

.empty-state h3 {
    font-size: 2rem;
    color: white;
    margin-bottom: 15px;
}

.empty-state p {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.8);
    margin-bottom: 30px;
}

.empty-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(45deg, #007AFF, #5856D6);
    color: white;
    box-shadow: 0 4px 15px rgba(0,122,255,0.3);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.btn-primary:hover {
    box-shadow: 0 6px 20px rgba(0,122,255,0.4);
}

.btn-outline:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

.btn i {
    margin-right: 8px;
}

@media (max-width: 768px) {
    .section-title {
        font-size: 2.5rem;
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .article-card {
        padding: 20px;
    }
    
    .pagination {
        flex-direction: column;
        gap: 15px;
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}
</style>

<%- include('../partials/footer') %>
