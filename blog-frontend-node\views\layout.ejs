<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    
    <!-- CSS样式 -->
    <link rel="stylesheet" href="/css/main.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Chart.js for data visualization -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Meta tags -->
    <meta name="description" content="现代博客系统 - 分享知识，传递价值">
    <meta name="keywords" content="博客,技术,分享,知识">
    <meta name="author" content="现代博客">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<%= title %>">
    <meta property="og:description" content="现代博客系统 - 分享知识，传递价值">
    <meta property="og:type" content="website">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar" id="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="/" class="brand-link">
                    <i class="fas fa-blog"></i>
                    <span>现代博客</span>
                </a>
            </div>
            
            <div class="nav-menu" id="navMenu">
                <a href="/" class="nav-link <%= currentPage === 'home' ? 'active' : '' %>">首页</a>
                <a href="/articles" class="nav-link <%= currentPage === 'articles' ? 'active' : '' %>">文章</a>
                <a href="/categories" class="nav-link <%= currentPage === 'categories' ? 'active' : '' %>">分类</a>
                <a href="/tags" class="nav-link <%= currentPage === 'tags' ? 'active' : '' %>">标签</a>
                <a href="/about" class="nav-link <%= currentPage === 'about' ? 'active' : '' %>">关于</a>
            </div>
            
            <div class="nav-actions">
                <div class="search-container">
                    <form action="/search" method="GET" class="search-form">
                        <input type="text" name="q" placeholder="搜索文章..." class="search-input" value="<%= typeof query !== 'undefined' ? query : '' %>">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                
                <div class="auth-buttons">
                    <% if (user) { %>
                        <div class="user-menu">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                                <span><%= user.username %></span>
                            </div>
                            <div class="user-dropdown">
                                <% if (user.role === 'admin') { %>
                                    <a href="/admin" class="dropdown-item">
                                        <i class="fas fa-cog"></i>
                                        管理后台
                                    </a>
                                <% } %>
                                <form action="/admin/logout" method="POST" style="display: inline;">
                                    <button type="submit" class="dropdown-item logout-btn">
                                        <i class="fas fa-sign-out-alt"></i>
                                        退出登录
                                    </button>
                                </form>
                            </div>
                        </div>
                    <% } else { %>
                        <a href="/admin/login" class="btn btn-outline">登录</a>
                    <% } %>
                </div>
                
                <button class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Flash消息 -->
    <% if (success_msg && success_msg.length > 0) { %>
        <div class="alert alert-success">
            <i class="fas fa-check-circle"></i>
            <%= success_msg %>
        </div>
    <% } %>
    
    <% if (error_msg && error_msg.length > 0) { %>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-circle"></i>
            <%= error_msg %>
        </div>
    <% } %>
    
    <% if (typeof error !== 'undefined' && error) { %>
        <div class="alert alert-error">
            <i class="fas fa-exclamation-triangle"></i>
            <%= error %>
        </div>
    <% } %>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <%- body %>
    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <i class="fas fa-blog"></i>
                    <span>现代博客</span>
                </div>
                <div class="footer-links">
                    <a href="/">首页</a>
                    <a href="/articles">文章</a>
                    <a href="/categories">分类</a>
                    <a href="/about">关于</a>
                </div>
                <div class="footer-social">
                    <a href="#" class="social-link"><i class="fab fa-github"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="social-link"><i class="fab fa-linkedin"></i></a>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 现代博客. 保留所有权利.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script src="/js/main.js?v=<%= Date.now() %>"></script>

    <!-- 页面特定的JavaScript -->
    <% if (typeof pageScript !== 'undefined') { %>
        <script src="/js/<%= pageScript %>?v=<%= Date.now() %>"></script>
    <% } %>
</body>
</html>
