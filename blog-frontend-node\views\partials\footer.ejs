    </main>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- 品牌信息 -->
                <div class="footer-section">
                    <div class="footer-brand">
                        <i class="fas fa-blog"></i>
                        <span>博客系统</span>
                    </div>
                    <p style="margin-top: var(--spacing-md); color: var(--text-secondary); line-height: 1.6;">
                        分享知识，传递价值。一个现代化的博客平台，致力于为用户提供优质的阅读和写作体验。
                    </p>
                    <div class="footer-social">
                        <a href="#" class="social-link" title="GitHub">
                            <i class="fab fa-github"></i>
                        </a>
                        <a href="#" class="social-link" title="Twitter">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-link" title="微博">
                            <i class="fab fa-weibo"></i>
                        </a>
                        <a href="#" class="social-link" title="邮箱">
                            <i class="fas fa-envelope"></i>
                        </a>
                    </div>
                </div>

                <!-- 快速链接 -->
                <div class="footer-section">
                    <h4 style="color: var(--text-primary); margin-bottom: var(--spacing-md); font-weight: 600;">快速链接</h4>
                    <div class="footer-links">
                        <a href="/">首页</a>
                        <a href="/articles">文章列表</a>
                        <a href="/categories">分类浏览</a>
                        <a href="/tags">标签云</a>
                        <a href="/about">关于我们</a>
                    </div>
                </div>

                <!-- 分类链接 -->
                <div class="footer-section">
                    <h4 style="color: var(--text-primary); margin-bottom: var(--spacing-md); font-weight: 600;">热门分类</h4>
                    <div class="footer-links">
                        <a href="/articles/category/tech">技术分享</a>
                        <a href="/articles/category/life">生活随笔</a>
                        <a href="/articles/category/tutorial">教程指南</a>
                        <a href="/articles/category/review">产品评测</a>
                        <a href="/categories">查看全部</a>
                    </div>
                </div>

                <!-- 联系信息 -->
                <div class="footer-section">
                    <h4 style="color: var(--text-primary); margin-bottom: var(--spacing-md); font-weight: 600;">联系我们</h4>
                    <div class="footer-links">
                        <a href="/contact">联系方式</a>
                        <a href="/feedback">意见反馈</a>
                        <a href="/help">帮助中心</a>
                        <a href="/privacy">隐私政策</a>
                        <a href="/terms">服务条款</a>
                    </div>
                </div>
            </div>

            <!-- 版权信息 -->
            <div class="footer-bottom">
                <p>&copy; <%= new Date().getFullYear() %> 博客系统. 保留所有权利.</p>
                <p style="margin-top: var(--spacing-xs); font-size: var(--font-size-xs);">
                    Powered by Node.js & Express | 设计灵感来源于现代化Web设计
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // 移动端导航菜单切换
        document.addEventListener('DOMContentLoaded', function() {
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');

            if (navToggle && navMenu) {
                navToggle.addEventListener('click', function() {
                    navToggle.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });
            }

            // 平滑滚动到锚点
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 英雄区域滚动指示器
            const scrollArrow = document.querySelector('.scroll-arrow');
            if (scrollArrow) {
                scrollArrow.addEventListener('click', function() {
                    const nextSection = document.querySelector('.featured-section, .stats-section');
                    if (nextSection) {
                        nextSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            }

            // 导航栏滚动效果
            let lastScrollTop = 0;
            const navbar = document.querySelector('.navbar');

            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // 向下滚动，隐藏导航栏
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // 向上滚动，显示导航栏
                    navbar.style.transform = 'translateY(0)';
                }

                lastScrollTop = scrollTop;
            });
        });
    </script>
</body>
</html>
