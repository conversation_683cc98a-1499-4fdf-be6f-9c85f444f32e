<%- include('partials/header') %>

<!-- 搜索页面 -->
<section class="search-section">
    <div class="container">
        <div class="section-header">
            <h1 class="section-title">
                <i class="fas fa-search"></i>
                搜索结果
            </h1>
            <% if (query) { %>
                <p class="section-subtitle">
                    搜索关键词：<span class="search-query">"<%= query %>"</span>
                    <% if (results && results.length > 0) { %>
                        - 找到 <%= results.length %> 篇相关文章
                    <% } %>
                </p>
            <% } else { %>
                <p class="section-subtitle">请输入搜索关键词</p>
            <% } %>
        </div>
        
        <!-- 搜索框 -->
        <div class="search-form-wrapper">
            <form class="search-form" action="/search" method="GET">
                <div class="search-input-group">
                    <input type="text" 
                           name="q" 
                           class="search-input" 
                           placeholder="输入关键词搜索文章..." 
                           value="<%= query || '' %>"
                           required>
                    <button type="submit" class="search-btn">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                </div>
            </form>
        </div>
        
        <% if (query) { %>
            <% if (results && results.length > 0) { %>
                <!-- 搜索结果 -->
                <div class="search-results">
                    <% results.forEach(article => { %>
                        <article class="search-result-item">
                            <a href="/articles/<%= article._id %>" class="result-link">
                                <h3 class="result-title"><%= article.title %></h3>
                                <div class="result-meta">
                                    <span class="result-author">
                                        <i class="fas fa-user"></i>
                                        <%= article.author ? article.author.username : '匿名' %>
                                    </span>
                                    <span class="result-date">
                                        <i class="fas fa-calendar"></i>
                                        <%= new Date(article.createdAt).toLocaleDateString('zh-CN') %>
                                    </span>
                                    <% if (article.category) { %>
                                    <span class="result-category">
                                        <i class="fas fa-folder"></i>
                                        <%= article.category.name %>
                                    </span>
                                    <% } %>
                                    <span class="result-views">
                                        <i class="fas fa-eye"></i>
                                        <%= article.viewCount || 0 %> 次浏览
                                    </span>
                                </div>
                                <div class="result-excerpt">
                                    <%= article.content.substring(0, 200) %>...
                                </div>
                                <% if (article.tags && article.tags.length > 0) { %>
                                <div class="result-tags">
                                    <% article.tags.slice(0, 3).forEach(tag => { %>
                                        <span class="tag">#<%= tag %></span>
                                    <% }) %>
                                </div>
                                <% } %>
                            </a>
                        </article>
                    <% }) %>
                </div>
                
                <!-- 分页 -->
                <% if (pagination && pagination.totalPages > 1) { %>
                <div class="pagination-wrapper">
                    <nav class="pagination">
                        <% if (pagination.currentPage > 1) { %>
                            <a href="/search?q=<%= encodeURIComponent(query) %>&page=<%= pagination.currentPage - 1 %>" class="pagination-btn">
                                <i class="fas fa-chevron-left"></i>
                                上一页
                            </a>
                        <% } %>
                        
                        <div class="pagination-info">
                            第 <%= pagination.currentPage %> 页，共 <%= pagination.totalPages %> 页
                        </div>
                        
                        <% if (pagination.currentPage < pagination.totalPages) { %>
                            <a href="/search?q=<%= encodeURIComponent(query) %>&page=<%= pagination.currentPage + 1 %>" class="pagination-btn">
                                下一页
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        <% } %>
                    </nav>
                </div>
                <% } %>
            <% } else { %>
                <!-- 无搜索结果 -->
                <div class="no-results">
                    <div class="no-results-icon">
                        <i class="fas fa-search-minus"></i>
                    </div>
                    <h3>未找到相关文章</h3>
                    <p>没有找到包含 "<%= query %>" 的文章，请尝试其他关键词。</p>
                    <div class="search-suggestions">
                        <h4>搜索建议：</h4>
                        <ul>
                            <li>检查关键词拼写是否正确</li>
                            <li>尝试使用更简单的关键词</li>
                            <li>使用同义词或相关词汇</li>
                            <li>减少关键词数量</li>
                        </ul>
                    </div>
                    <div class="no-results-actions">
                        <a href="/articles" class="btn btn-primary">
                            <i class="fas fa-book-open"></i>
                            浏览所有文章
                        </a>
                        <a href="/categories" class="btn btn-outline">
                            <i class="fas fa-list"></i>
                            查看分类
                        </a>
                    </div>
                </div>
            <% } %>
        <% } else { %>
            <!-- 搜索提示 -->
            <div class="search-tips">
                <div class="tips-icon">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <h3>搜索提示</h3>
                <div class="tips-grid">
                    <div class="tip-item">
                        <i class="fas fa-keyboard"></i>
                        <h4>关键词搜索</h4>
                        <p>输入文章标题或内容中的关键词</p>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-quote-left"></i>
                        <h4>精确搜索</h4>
                        <p>使用引号进行精确短语搜索</p>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-tags"></i>
                        <h4>标签搜索</h4>
                        <p>搜索特定标签的相关文章</p>
                    </div>
                    <div class="tip-item">
                        <i class="fas fa-filter"></i>
                        <h4>分类浏览</h4>
                        <p>通过分类快速找到相关内容</p>
                    </div>
                </div>
            </div>
        <% } %>
    </div>
</section>

<style>
.search-section {
    padding: 80px 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 80vh;
}

.section-header {
    text-align: center;
    margin-bottom: 40px;
}

.section-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.section-title i {
    margin-right: 15px;
    color: #ffd700;
}

.section-subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
}

.search-query {
    color: #ffd700;
    font-weight: 600;
}

.search-form-wrapper {
    max-width: 600px;
    margin: 0 auto 60px;
}

.search-input-group {
    display: flex;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 50px;
    padding: 8px;
    border: 1px solid rgba(255,255,255,0.2);
}

.search-input {
    flex: 1;
    background: transparent;
    border: none;
    padding: 15px 20px;
    color: white;
    font-size: 1.1rem;
    outline: none;
}

.search-input::placeholder {
    color: rgba(255,255,255,0.7);
}

.search-btn {
    background: linear-gradient(45deg, #007AFF, #5856D6);
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 40px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,122,255,0.4);
}

.search-results {
    display: flex;
    flex-direction: column;
    gap: 25px;
    margin-bottom: 40px;
}

.search-result-item {
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.search-result-item:hover {
    background: rgba(255,255,255,0.15);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.result-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.result-title {
    font-size: 1.4rem;
    font-weight: 600;
    color: white;
    margin-bottom: 12px;
    line-height: 1.4;
}

.result-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 12px;
    font-size: 0.9rem;
    color: rgba(255,255,255,0.8);
}

.result-meta span {
    display: flex;
    align-items: center;
    gap: 5px;
}

.result-excerpt {
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
    margin-bottom: 12px;
}

.result-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.tag {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.no-results, .search-tips {
    text-align: center;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 60px 40px;
    border: 1px solid rgba(255,255,255,0.2);
}

.no-results-icon, .tips-icon {
    font-size: 4rem;
    color: rgba(255,255,255,0.6);
    margin-bottom: 30px;
}

.no-results h3, .search-tips h3 {
    font-size: 2rem;
    color: white;
    margin-bottom: 15px;
}

.no-results p {
    font-size: 1.1rem;
    color: rgba(255,255,255,0.8);
    margin-bottom: 30px;
}

.search-suggestions {
    text-align: left;
    max-width: 400px;
    margin: 30px auto;
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 15px;
}

.search-suggestions h4 {
    color: white;
    margin-bottom: 15px;
}

.search-suggestions ul {
    color: rgba(255,255,255,0.9);
    padding-left: 20px;
}

.search-suggestions li {
    margin-bottom: 8px;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
    margin-top: 40px;
}

.tip-item {
    background: rgba(255,255,255,0.1);
    padding: 25px;
    border-radius: 15px;
    border: 1px solid rgba(255,255,255,0.2);
}

.tip-item i {
    font-size: 2rem;
    color: #ffd700;
    margin-bottom: 15px;
}

.tip-item h4 {
    color: white;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.tip-item p {
    color: rgba(255,255,255,0.8);
    font-size: 0.9rem;
    line-height: 1.5;
}

.no-results-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 30px;
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 12px 24px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: linear-gradient(45deg, #007AFF, #5856D6);
    color: white;
    box-shadow: 0 4px 15px rgba(0,122,255,0.3);
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

.btn:hover {
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.btn-primary:hover {
    box-shadow: 0 6px 20px rgba(0,122,255,0.4);
}

.btn-outline:hover {
    background: rgba(255,255,255,0.1);
    border-color: rgba(255,255,255,0.5);
}

.btn i {
    margin-right: 8px;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 40px;
}

.pagination {
    display: flex;
    align-items: center;
    gap: 20px;
    background: rgba(255,255,255,0.1);
    backdrop-filter: blur(10px);
    padding: 15px 30px;
    border-radius: 50px;
    border: 1px solid rgba(255,255,255,0.2);
}

.pagination-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 20px;
    background: rgba(255,255,255,0.2);
    color: white;
    text-decoration: none;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.pagination-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.pagination-info {
    color: rgba(255,255,255,0.9);
    font-weight: 500;
}

@media (max-width: 768px) {
    .section-title {
        font-size: 2.5rem;
    }
    
    .search-input-group {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
        border-radius: 20px;
    }
    
    .search-btn {
        border-radius: 15px;
        justify-content: center;
    }
    
    .result-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .tips-grid {
        grid-template-columns: 1fr;
    }
    
    .no-results-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .pagination {
        flex-direction: column;
        gap: 15px;
    }
}
</style>

<%- include('partials/footer') %>
