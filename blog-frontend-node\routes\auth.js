const express = require('express');
const axios = require('axios');
const router = express.Router();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000/api';

// 创建axios实例
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000
});

// 注册页面
router.get('/register', (req, res) => {
    res.render('register', {
        title: '用户注册',
        currentPage: 'register'
    });
});

// 登录页面
router.get('/login', (req, res) => {
    res.render('login', {
        title: '用户登录',
        currentPage: 'login'
    });
});

// 处理注册
router.post('/register', async (req, res) => {
    try {
        const { username, email, password, confirmPassword, isAdmin } = req.body;
        
        // 验证密码匹配
        if (password !== confirmPassword) {
            req.flash('error_msg', '密码不匹配');
            return res.redirect('/auth/register');
        }
        
        // 准备注册数据
        const registerData = {
            username,
            email,
            password
        };
        
        // 如果选择了管理员选项，添加角色
        if (isAdmin === 'true') {
            registerData.role = 'admin';
        }
        
        // 调用后端API注册
        const response = await api.post('/auth/register', registerData);
        
        if (response.data.success) {
            req.flash('success_msg', '注册成功！请登录');
            res.redirect('/auth/login');
        } else {
            req.flash('error_msg', response.data.message || '注册失败');
            res.redirect('/auth/register');
        }
    } catch (error) {
        console.error('注册错误:', error.response?.data || error.message);
        req.flash('error_msg', error.response?.data?.message || '注册失败，请重试');
        res.redirect('/auth/register');
    }
});

// 处理登录
router.post('/login', async (req, res) => {
    try {
        const { email, password } = req.body;

        // 调用后端API登录
        const response = await api.post('/auth/login', {
            email,
            password
        });

        console.log('登录响应:', response.data);

        if (response.data.success) {
            // 保存用户信息到session
            req.session.user = response.data.user;
            req.session.token = response.data.token;

            req.flash('success_msg', '登录成功！');

            // 根据用户角色重定向
            if (response.data.user.role === 'admin') {
                res.redirect('/admin');
            } else {
                res.redirect('/');
            }
        } else {
            req.flash('error_msg', response.data.message || '登录失败');
            res.redirect('/auth/login');
        }
    } catch (error) {
        console.error('登录错误:', error.response?.data || error.message);
        console.error('完整错误对象:', error);
        req.flash('error_msg', error.response?.data?.message || '登录失败，请重试');
        res.redirect('/auth/login');
    }
});

// 退出登录
router.get('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            console.error('退出登录错误:', err);
        }
        res.redirect('/');
    });
});

module.exports = router;
