EJS Embedded JavaScript templates
{Usage}: ejs [options ...] template-file [data variables ...]

{Options}:
  -o,     --output-file FILE            Write the rendered output to FIL<PERSON> rather than stdout.
  -f,     --data-file FILE              Must be JSON-formatted. Use parsed input from FILE as data for rendering.
  -i,     --data-input STRING           Must be JSON-formatted and URI-encoded. Use parsed input from STRING as data for rendering.
  -m,     --delimiter CHARACTER         Use CHARACTER with angle brackets for open/close (defaults to %).
  -p,     --open-delimiter CHARACTER    Use CHARACTER instead of left angle bracket to open.
  -c,     --close-delimiter CHARACTER   Use CHARACTER instead of right angle bracket to close.
  -s,     --strict                      When set to `true`, generated function is in strict mode
  -n      --no-with                     Use 'locals' object for vars rather than using `with` (implies --strict).
  -l      --locals-name                 Name to use for the object storing local variables when not using `with`.
  -w      --rm-whitespace               Remove all safe-to-remove whitespace, including leading and trailing whitespace.
  -d      --debug                       Outputs generated function body
  -h,     --help                        Display this help message.
  -V/v,   --version                     Display the EJS version.

{Examples}:
  ejs -m $ ./test/fixtures/user.ejs -f ./user_data.json
  ejs -m $ ./test/fixtures/user.ejs name=Lerxst
  ejs -p [ -c ] ./template_file.ejs -o ./output.html
  ejs -n -l _ ./some_template.ejs -f ./data_file.json
  ejs -w ./template_with_whitspace.ejs -o ./output_file.html
