{"name": "blog-frontend-node", "version": "1.0.0", "description": "现代博客系统前端 - Node.js + Express + EJS", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["blog", "express", "ejs", "nodejs"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "ejs": "^3.1.9", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-session": "^1.17.3", "connect-flash": "^0.1.1", "method-override": "^3.0.0", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1"}}