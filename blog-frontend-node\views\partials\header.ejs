<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= typeof title !== 'undefined' ? title + ' - ' : '' %>博客系统</title>

    <!-- CSS样式 -->
    <link rel="stylesheet" href="/css/main.css">

    <!-- Font Awesome图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Meta标签 -->
    <meta name="description" content="<%= typeof description !== 'undefined' ? description : '一个现代化的博客系统，分享知识，传递价值' %>">
    <meta name="keywords" content="博客,技术,分享,知识">
    <meta name="author" content="博客系统">

    <!-- Open Graph -->
    <meta property="og:title" content="<%= typeof title !== 'undefined' ? title : '博客系统' %>">
    <meta property="og:description" content="<%= typeof description !== 'undefined' ? description : '一个现代化的博客系统，分享知识，传递价值' %>">
    <meta property="og:type" content="website">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <!-- 品牌Logo -->
            <div class="nav-brand">
                <a href="/" class="brand-link">
                    <i class="fas fa-blog"></i>
                    <span>博客系统</span>
                </a>
            </div>

            <!-- 导航菜单 -->
            <div class="nav-menu" id="navMenu">
                <a href="/" class="nav-link <%= typeof currentPage !== 'undefined' && currentPage === 'home' ? 'active' : '' %>">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a href="/articles" class="nav-link <%= typeof currentPage !== 'undefined' && currentPage === 'articles' ? 'active' : '' %>">
                    <i class="fas fa-file-alt"></i>
                    文章
                </a>
                <a href="/categories" class="nav-link <%= typeof currentPage !== 'undefined' && currentPage === 'categories' ? 'active' : '' %>">
                    <i class="fas fa-folder"></i>
                    分类
                </a>
                <a href="/tags" class="nav-link <%= typeof currentPage !== 'undefined' && currentPage === 'tags' ? 'active' : '' %>">
                    <i class="fas fa-tags"></i>
                    标签
                </a>
            </div>

            <!-- 导航操作区域 -->
            <div class="nav-actions">
                <!-- 搜索框 -->
                <div class="search-container">
                    <form class="search-form" action="/search" method="GET">
                        <input type="text" name="q" class="search-input" placeholder="搜索文章..." value="<%= typeof searchQuery !== 'undefined' ? searchQuery : '' %>">
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- 用户认证区域 -->
                <div class="auth-buttons">
                    <% if (typeof user !== 'undefined' && user) { %>
                        <!-- 已登录用户菜单 -->
                        <div class="user-menu">
                            <div class="user-avatar">
                                <i class="fas fa-user"></i>
                                <span><%= user.username %></span>
                                <i class="fas fa-chevron-down"></i>
                            </div>
                            <div class="user-dropdown">
                                <% if (user.role === 'admin') { %>
                                    <a href="/admin" class="dropdown-item">
                                        <i class="fas fa-cog"></i>
                                        管理后台
                                    </a>
                                <% } %>
                                <a href="/profile" class="dropdown-item">
                                    <i class="fas fa-user-circle"></i>
                                    个人资料
                                </a>
                                <a href="/settings" class="dropdown-item">
                                    <i class="fas fa-cog"></i>
                                    设置
                                </a>
                                <hr style="margin: 8px 0; border: none; border-top: 1px solid var(--text-tertiary);">
                                <form action="/auth/logout" method="POST" style="margin: 0;">
                                    <button type="submit" class="dropdown-item logout-btn">
                                        <i class="fas fa-sign-out-alt"></i>
                                        退出登录
                                    </button>
                                </form>
                            </div>
                        </div>
                    <% } else { %>
                        <!-- 未登录用户按钮 -->
                        <a href="/auth/login" class="btn btn-outline">
                            <i class="fas fa-sign-in-alt"></i>
                            登录
                        </a>
                        <a href="/auth/register" class="btn btn-primary">
                            <i class="fas fa-user-plus"></i>
                            注册
                        </a>
                    <% } %>
                </div>

                <!-- 移动端菜单切换按钮 -->
                <button class="nav-toggle" id="navToggle">
                    <span></span>
                    <span></span>
                    <span></span>
                </button>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域开始 -->
    <main class="main-content">
