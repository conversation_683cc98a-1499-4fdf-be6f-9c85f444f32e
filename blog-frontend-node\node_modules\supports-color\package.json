{"name": "supports-color", "version": "7.2.0", "description": "Detect whether a terminal supports color", "license": "MIT", "repository": "chalk/supports-color", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava"}, "files": ["index.js", "browser.js"], "keywords": ["color", "colour", "colors", "terminal", "console", "cli", "ansi", "styles", "tty", "rgb", "256", "shell", "xterm", "command-line", "support", "supports", "capability", "detect", "truecolor", "16m"], "dependencies": {"has-flag": "^4.0.0"}, "devDependencies": {"ava": "^1.4.1", "import-fresh": "^3.0.0", "xo": "^0.24.0"}, "browser": "browser.js"}