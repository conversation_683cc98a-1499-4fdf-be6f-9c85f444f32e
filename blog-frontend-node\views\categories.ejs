<%- include('partials/header', { title: '文章分类', currentPage: 'categories' }) %>

<div class="categories-page">
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1 class="page-title">文章分类</h1>
            <p class="page-subtitle">浏览所有文章分类，发现感兴趣的内容</p>
        </div>

        <!-- 分类网格 -->
        <div class="categories-grid">
            <% if (categories && categories.length > 0) { %>
                <% categories.forEach(category => { %>
                    <div class="category-card">
                        <div class="category-icon">
                            <i class="fas fa-<%= category.icon || 'folder' %>"></i>
                        </div>
                        <div class="category-content">
                            <h3 class="category-name">
                                <a href="/articles/category/<%= category._id %>">
                                    <%= category.name %>
                                </a>
                            </h3>
                            <p class="category-description">
                                <%= category.description || '暂无描述' %>
                            </p>
                            <div class="category-stats">
                                <span class="article-count">
                                    <i class="fas fa-file-alt"></i>
                                    <%= category.articleCount || 0 %> 篇文章
                                </span>
                            </div>
                        </div>
                        <div class="category-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                <% }) %>
            <% } else { %>
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3>暂无分类</h3>
                    <p>还没有创建任何分类</p>
                </div>
            <% } %>
        </div>
    </div>
</div>

<%- include('partials/footer') %>

<style>
.categories-page {
    padding: var(--spacing-2xl) 0;
    min-height: 70vh;
}

.page-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.page-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.page-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.category-card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    transition: all var(--transition-normal);
    cursor: pointer;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    overflow: hidden;
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-large);
}

.category-card:hover::before {
    transform: scaleX(1);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: var(--font-size-xl);
    flex-shrink: 0;
}

.category-content {
    flex: 1;
}

.category-name {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
}

.category-name a {
    color: var(--text-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.category-name a:hover {
    color: var(--primary-color);
}

.category-description {
    color: var(--text-secondary);
    margin: 0 0 var(--spacing-md) 0;
    line-height: 1.5;
}

.category-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.article-count {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.article-count i {
    color: var(--primary-color);
}

.category-arrow {
    color: var(--text-tertiary);
    font-size: var(--font-size-lg);
    transition: all var(--transition-fast);
}

.category-card:hover .category-arrow {
    color: var(--primary-color);
    transform: translateX(4px);
}

.empty-state {
    grid-column: 1 / -1;
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .categories-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }
    
    .category-card {
        padding: var(--spacing-lg);
    }
    
    .category-icon {
        width: 50px;
        height: 50px;
        font-size: var(--font-size-lg);
    }
    
    .page-title {
        font-size: var(--font-size-2xl);
    }
    
    .page-subtitle {
        font-size: var(--font-size-base);
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.category-card {
    animation: fadeInUp 0.6s ease-out forwards;
}

.category-card:nth-child(2) {
    animation-delay: 0.1s;
}

.category-card:nth-child(3) {
    animation-delay: 0.2s;
}

.category-card:nth-child(4) {
    animation-delay: 0.3s;
}

.category-card:nth-child(5) {
    animation-delay: 0.4s;
}

.category-card:nth-child(6) {
    animation-delay: 0.5s;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 分类卡片点击事件
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', function() {
            const link = this.querySelector('.category-name a');
            if (link) {
                window.location.href = link.href;
            }
        });
    });
});
</script>
