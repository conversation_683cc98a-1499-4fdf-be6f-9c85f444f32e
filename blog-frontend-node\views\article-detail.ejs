<%- include('partials/header') %>

<article class="article-detail">
    <div class="container">
        <!-- 文章头部 -->
        <header class="article-header">
            <div class="article-breadcrumb">
                <a href="/">首页</a>
                <i class="fas fa-chevron-right"></i>
                <a href="/articles">文章</a>
                <% if (article.category) { %>
                <i class="fas fa-chevron-right"></i>
                <a href="/articles/category/<%= article.category._id %>">
                    <%= article.category.name %>
                </a>
                <% } %>
                <i class="fas fa-chevron-right"></i>
                <span>正文</span>
            </div>
            
            <h1 class="article-title"><%= article.title %></h1>
            
            <div class="article-meta">
                <div class="meta-left">
                    <span class="article-author">
                        <i class="fas fa-user"></i>
                        <%= article.author ? article.author.username : '匿名' %>
                    </span>
                    <span class="article-date">
                        <i class="fas fa-calendar"></i>
                        <%= new Date(article.createdAt).toLocaleDateString('zh-CN', { 
                            year: 'numeric', 
                            month: 'long', 
                            day: 'numeric' 
                        }) %>
                    </span>
                    <% if (article.category) { %>
                    <span class="article-category">
                        <i class="fas fa-folder"></i>
                        <a href="/articles/category/<%= article.category._id %>">
                            <%= article.category.name %>
                        </a>
                    </span>
                    <% } %>
                </div>
                <div class="meta-right">
                    <span class="article-views">
                        <i class="fas fa-eye"></i>
                        <%= article.viewCount || 0 %> 次浏览
                    </span>
                </div>
            </div>
            
            <% if (article.tags && article.tags.length > 0) { %>
            <div class="article-tags">
                <% article.tags.forEach(tag => { %>
                    <a href="/articles/tag/<%= encodeURIComponent(tag) %>" class="tag">#<%= tag %></a>
                <% }) %>
            </div>
            <% } %>
        </header>
        
        <!-- 文章内容 -->
        <div class="article-content">
            <div class="content-wrapper">
                <%- article.content.replace(/\n/g, '<br>') %>
            </div>
        </div>
        
        <!-- 文章底部操作 -->
        <footer class="article-footer">
            <div class="article-actions">
                <button class="action-btn like-btn" data-article-id="<%= article._id %>">
                    <i class="fas fa-heart"></i>
                    <span class="like-count"><%= article.likeCount || 0 %></span>
                </button>
                <button class="action-btn share-btn" onclick="shareArticle()">
                    <i class="fas fa-share"></i>
                    分享
                </button>
            </div>
            
            <div class="article-navigation">
                <% if (typeof prevArticle !== 'undefined' && prevArticle) { %>
                <a href="/articles/<%= prevArticle._id %>" class="nav-link prev-article">
                    <i class="fas fa-chevron-left"></i>
                    <div class="nav-content">
                        <span class="nav-label">上一篇</span>
                        <span class="nav-title"><%= prevArticle.title %></span>
                    </div>
                </a>
                <% } %>
                
                <% if (typeof nextArticle !== 'undefined' && nextArticle) { %>
                <a href="/articles/<%= nextArticle._id %>" class="nav-link next-article">
                    <div class="nav-content">
                        <span class="nav-label">下一篇</span>
                        <span class="nav-title"><%= nextArticle.title %></span>
                    </div>
                    <i class="fas fa-chevron-right"></i>
                </a>
                <% } %>
            </div>
        </footer>
    </div>
</article>

<!-- 相关文章 -->
<% if (relatedArticles && relatedArticles.length > 0) { %>
<section class="related-articles">
    <div class="container">
        <h2 class="section-title">相关文章</h2>
        <div class="articles-grid">
            <% relatedArticles.forEach(related => { %>
                <article class="article-card">
                    <a href="/articles/<%= related._id %>" class="article-link">
                        <h3 class="article-title"><%= related.title %></h3>
                        <div class="article-meta">
                            <span class="article-date">
                                <i class="fas fa-calendar"></i>
                                <%= new Date(related.createdAt).toLocaleDateString('zh-CN') %>
                            </span>
                            <span class="article-views">
                                <i class="fas fa-eye"></i>
                                <%= related.viewCount || 0 %>
                            </span>
                        </div>
                        <div class="article-excerpt">
                            <%= related.content.substring(0, 100) %>...
                        </div>
                    </a>
                </article>
            <% }) %>
        </div>
    </div>
</section>
<% } %>

<!-- 评论区域 -->
<section class="comments-section">
    <div class="container">
        <h2 class="section-title">评论 (<%= comments ? comments.length : 0 %>)</h2>
        
        <!-- 评论表单 -->
        <div class="comment-form-wrapper">
            <form class="comment-form" action="/articles/<%= article._id %>/comments" method="POST">
                <div class="form-group">
                    <label for="author" class="form-label">姓名</label>
                    <input type="text" id="author" name="author" class="form-input" placeholder="请输入您的姓名" required>
                </div>
                <div class="form-group">
                    <label for="email" class="form-label">邮箱</label>
                    <input type="email" id="email" name="email" class="form-input" placeholder="请输入您的邮箱（不会公开）" required>
                </div>
                <div class="form-group">
                    <label for="content" class="form-label">评论内容</label>
                    <textarea id="content" name="content" class="form-textarea" rows="4" placeholder="写下您的评论..." required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-comment"></i>
                    发表评论
                </button>
            </form>
        </div>
        
        <!-- 评论列表 -->
        <% if (comments && comments.length > 0) { %>
        <div class="comments-list">
            <% comments.forEach(comment => { %>
                <div class="comment-item">
                    <div class="comment-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="comment-content">
                        <div class="comment-header">
                            <span class="comment-author"><%= comment.author %></span>
                            <span class="comment-date">
                                <%= new Date(comment.createdAt).toLocaleDateString('zh-CN', { 
                                    year: 'numeric', 
                                    month: 'short', 
                                    day: 'numeric',
                                    hour: '2-digit',
                                    minute: '2-digit'
                                }) %>
                            </span>
                        </div>
                        <div class="comment-body">
                            <%= comment.content %>
                        </div>
                    </div>
                </div>
            <% }) %>
        </div>
        <% } else { %>
        <div class="empty-comments">
            <i class="fas fa-comments"></i>
            <p>还没有评论，快来发表第一个评论吧！</p>
        </div>
        <% } %>
    </div>
</section>

<%- include('partials/footer') %>

<style>
/* 文章详情样式 */
.article-detail {
    padding: var(--spacing-2xl) 0;
}

.article-header {
    margin-bottom: var(--spacing-2xl);
    text-align: center;
}

.article-breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.article-breadcrumb a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.article-breadcrumb a:hover {
    color: var(--primary-color);
}

.article-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.2;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-medium);
}

.meta-left,
.meta-right {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.article-tags {
    display: flex;
    justify-content: center;
    gap: var(--spacing-sm);
    flex-wrap: wrap;
}

.article-content {
    margin-bottom: var(--spacing-2xl);
}

.content-wrapper {
    background: var(--surface-color);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-large);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: var(--font-size-lg);
    line-height: 1.8;
    color: var(--text-primary);
}

.article-footer {
    margin-bottom: var(--spacing-2xl);
}

.article-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.action-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    background: var(--surface-color);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-medium);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-fast);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.article-navigation {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.nav-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--surface-color);
    border-radius: var(--radius-medium);
    text-decoration: none;
    color: var(--text-primary);
    transition: all var(--transition-fast);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-link:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.nav-content {
    flex: 1;
}

.nav-label {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
}

.nav-title {
    font-weight: 500;
    line-height: 1.4;
}

.next-article {
    text-align: right;
}

/* 相关文章 */
.related-articles {
    padding: var(--spacing-2xl) 0;
    background: var(--background-secondary);
}

/* 评论区域 */
.comments-section {
    padding: var(--spacing-2xl) 0;
}

.comment-form-wrapper {
    background: var(--surface-color);
    padding: var(--spacing-xl);
    border-radius: var(--radius-large);
    margin-bottom: var(--spacing-xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.comment-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-lg);
}

.comment-form .form-group:nth-child(3) {
    grid-column: 1 / -1;
}

.comment-form button {
    grid-column: 1 / -1;
    justify-self: start;
}

.comments-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.comment-item {
    display: flex;
    gap: var(--spacing-md);
    padding: var(--spacing-lg);
    background: var(--surface-color);
    border-radius: var(--radius-medium);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.comment-avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: var(--radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    flex-shrink: 0;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.comment-author {
    font-weight: 600;
    color: var(--text-primary);
}

.comment-date {
    font-size: var(--font-size-xs);
    color: var(--text-secondary);
}

.comment-body {
    color: var(--text-primary);
    line-height: 1.6;
}

.empty-comments {
    text-align: center;
    padding: var(--spacing-2xl);
    color: var(--text-secondary);
}

.empty-comments i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .article-title {
        font-size: var(--font-size-2xl);
    }
    
    .article-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
        text-align: center;
    }
    
    .article-navigation {
        grid-template-columns: 1fr;
    }
    
    .comment-form {
        grid-template-columns: 1fr;
    }
    
    .content-wrapper {
        padding: var(--spacing-lg);
    }
}
</style>

<script>
// 分享功能
function shareArticle() {
    if (navigator.share) {
        navigator.share({
            title: '<%= article.title %>',
            url: window.location.href
        });
    } else {
        // 复制链接到剪贴板
        navigator.clipboard.writeText(window.location.href).then(() => {
            showNotification('链接已复制到剪贴板', 'success');
        });
    }
}

// 点赞功能
document.addEventListener('DOMContentLoaded', function() {
    const likeBtn = document.querySelector('.like-btn');
    if (likeBtn) {
        likeBtn.addEventListener('click', function() {
            const articleId = this.dataset.articleId;
            // 这里可以添加点赞的AJAX请求
            console.log('点赞文章:', articleId);
        });
    }
});
</script>
