<%- include('partials/header') %>

<!-- 页面标题区域 -->
<section class="page-header">
    <div class="container">
        <div class="page-header-content">
            <h1 class="page-title">
                <% if (typeof category !== 'undefined' && category) { %>
                    <%= category.name %> - 文章列表
                <% } else if (typeof tag !== 'undefined' && tag) { %>
                    标签: <%= tag %> - 文章列表
                <% } else if (typeof query !== 'undefined' && query) { %>
                    搜索: <%= query %> - 搜索结果
                <% } else { %>
                    所有文章
                <% } %>
            </h1>
            <p class="page-subtitle">
                <% if (typeof category !== 'undefined' && category && category.description) { %>
                    <%= category.description %>
                <% } else if (typeof query !== 'undefined' && query) { %>
                    找到 <%= articles.length %> 篇相关文章
                <% } else { %>
                    探索所有精彩内容
                <% } %>
            </p>
        </div>
    </div>
</section>

<!-- 文章列表区域 -->
<section class="articles-section">
    <div class="container">
        <% if (articles && articles.length > 0) { %>
            <div class="articles-grid">
                <% articles.forEach(article => { %>
                    <article class="article-card">
                        <a href="/articles/<%= article._id %>" class="article-link">
                            <h3 class="article-title"><%= article.title %></h3>
                            <div class="article-meta">
                                <span class="article-author">
                                    <i class="fas fa-user"></i>
                                    <%= article.author ? article.author.username : '匿名' %>
                                </span>
                                <span class="article-date">
                                    <i class="fas fa-calendar"></i>
                                    <%= new Date(article.createdAt).toLocaleDateString('zh-CN') %>
                                </span>
                                <% if (article.category) { %>
                                <span class="article-category">
                                    <i class="fas fa-folder"></i>
                                    <a href="/articles/category/<%= article.category._id %>">
                                        <%= article.category.name %>
                                    </a>
                                </span>
                                <% } %>
                            </div>
                            <div class="article-excerpt">
                                <%= article.content.substring(0, 200) %>...
                            </div>
                            <% if (article.tags && article.tags.length > 0) { %>
                            <div class="article-tags">
                                <% article.tags.forEach(tag => { %>
                                    <a href="/articles/tag/<%= encodeURIComponent(tag) %>" class="tag">#<%= tag %></a>
                                <% }) %>
                            </div>
                            <% } %>
                            <div class="article-footer">
                                <span class="article-views">
                                    <i class="fas fa-eye"></i>
                                    <%= article.viewCount || 0 %> 次浏览
                                </span>
                                <span class="read-more">
                                    阅读更多
                                    <i class="fas fa-arrow-right"></i>
                                </span>
                            </div>
                        </a>
                    </article>
                <% }) %>
            </div>
            
            <!-- 分页 -->
            <% if (pagination && pagination.totalPages > 1) { %>
            <div class="pagination">
                <% if (pagination.currentPage > 1) { %>
                    <a href="?page=<%= pagination.currentPage - 1 %><%= typeof query !== 'undefined' && query ? '&q=' + encodeURIComponent(query) : '' %>" class="pagination-btn">
                        <i class="fas fa-chevron-left"></i>
                        上一页
                    </a>
                <% } %>
                
                <% for (let i = Math.max(1, pagination.currentPage - 2); i <= Math.min(pagination.totalPages, pagination.currentPage + 2); i++) { %>
                    <% if (i === pagination.currentPage) { %>
                        <span class="pagination-btn current"><%= i %></span>
                    <% } else { %>
                        <a href="?page=<%= i %><%= typeof query !== 'undefined' && query ? '&q=' + encodeURIComponent(query) : '' %>" class="pagination-btn"><%= i %></a>
                    <% } %>
                <% } %>
                
                <% if (pagination.currentPage < pagination.totalPages) { %>
                    <a href="?page=<%= pagination.currentPage + 1 %><%= typeof query !== 'undefined' && query ? '&q=' + encodeURIComponent(query) : '' %>" class="pagination-btn">
                        下一页
                        <i class="fas fa-chevron-right"></i>
                    </a>
                <% } %>
            </div>
            <% } %>
        <% } else { %>
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>
                    <% if (typeof query !== 'undefined' && query) { %>
                        未找到相关文章
                    <% } else { %>
                        暂无文章
                    <% } %>
                </h3>
                <p>
                    <% if (typeof query !== 'undefined' && query) { %>
                        尝试使用其他关键词搜索，或者<a href="/articles">浏览所有文章</a>
                    <% } else { %>
                        还没有发布任何文章，请稍后再来查看。
                    <% } %>
                </p>
                <div class="empty-actions">
                    <a href="/" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        返回首页
                    </a>
                    <% if (typeof query !== 'undefined' && query) { %>
                    <a href="/articles" class="btn btn-outline">
                        <i class="fas fa-list"></i>
                        浏览所有文章
                    </a>
                    <% } %>
                </div>
            </div>
        <% } %>
    </div>
</section>

<!-- 侧边栏（可选） -->
<aside class="articles-sidebar">
    <div class="container">
        <div class="sidebar-content">
            <!-- 搜索框 -->
            <div class="sidebar-widget">
                <h3 class="widget-title">搜索文章</h3>
                <form action="/search" method="GET" class="search-widget">
                    <div class="search-input-group">
                        <input 
                            type="text" 
                            name="q" 
                            placeholder="输入关键词..." 
                            class="search-input"
                            value="<%= typeof query !== 'undefined' ? query : '' %>"
                        >
                        <button type="submit" class="search-btn">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>
            
            <!-- 分类列表 -->
            <% if (categories && categories.length > 0) { %>
            <div class="sidebar-widget">
                <h3 class="widget-title">文章分类</h3>
                <ul class="category-list">
                    <% categories.forEach(cat => { %>
                        <li class="category-item">
                            <a href="/articles/category/<%= cat._id %>" class="category-link">
                                <i class="fas fa-folder"></i>
                                <%= cat.name %>
                                <span class="category-count">(<%= cat.articleCount || 0 %>)</span>
                            </a>
                        </li>
                    <% }) %>
                </ul>
            </div>
            <% } %>
            
            <!-- 标签云 -->
            <% if (tags && tags.length > 0) { %>
            <div class="sidebar-widget">
                <h3 class="widget-title">热门标签</h3>
                <div class="tag-cloud">
                    <% tags.forEach(tag => { %>
                        <a href="/articles/tag/<%= encodeURIComponent(tag.name) %>" class="tag-cloud-item">
                            #<%= tag.name %>
                            <span class="tag-count">(<%= tag.count %>)</span>
                        </a>
                    <% }) %>
                </div>
            </div>
            <% } %>
        </div>
    </div>
</aside>

<%- include('partials/footer') %>

<style>
/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: var(--spacing-3xl) 0;
    margin-bottom: var(--spacing-2xl);
    text-align: center;
}

.page-header-content {
    max-width: 800px;
    margin: 0 auto;
}

.page-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--spacing-md);
}

.page-subtitle {
    font-size: var(--font-size-lg);
    opacity: 0.9;
}

/* 文章区域 */
.articles-section {
    margin-bottom: var(--spacing-3xl);
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-2xl) 0;
}

.pagination-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-medium);
    text-decoration: none;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.pagination-btn:hover {
    background: var(--primary-color);
    color: white;
}

.pagination-btn.current {
    background: var(--primary-color);
    color: white;
}

/* 侧边栏 */
.articles-sidebar {
    background: var(--background-secondary);
    padding: var(--spacing-2xl) 0;
}

.sidebar-content {
    max-width: 300px;
    margin: 0 auto;
}

.sidebar-widget {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.search-input-group {
    display: flex;
    gap: var(--spacing-xs);
}

.search-input-group .search-input {
    flex: 1;
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-item {
    margin-bottom: var(--spacing-sm);
}

.category-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--text-secondary);
    text-decoration: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-small);
    transition: all var(--transition-fast);
}

.category-link:hover {
    background: var(--background-secondary);
    color: var(--primary-color);
}

.category-count {
    margin-left: auto;
    font-size: var(--font-size-xs);
}

.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
}

.tag-cloud-item {
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-small);
    font-size: var(--font-size-xs);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.tag-cloud-item:hover {
    background: var(--primary-color);
    color: white;
}

.tag-count {
    font-size: var(--font-size-xs);
    opacity: 0.7;
}

.empty-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: center;
    margin-top: var(--spacing-lg);
}

@media (max-width: 768px) {
    .page-title {
        font-size: var(--font-size-2xl);
    }
    
    .empty-actions {
        flex-direction: column;
        align-items: center;
    }
}
</style>
