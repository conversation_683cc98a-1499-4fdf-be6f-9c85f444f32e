<%- include('partials/header') %>

<!-- 英雄区域 -->
<section class="hero-section">
    <div class="hero-background">
        <div class="hero-overlay"></div>
    </div>
    <div class="hero-content">
        <h1 class="hero-title">分享知识，传递价值</h1>
        <p class="hero-subtitle">探索技术世界，记录成长足迹</p>
        <div class="hero-actions">
            <a href="/articles" class="btn btn-primary">
                <i class="fas fa-book-open"></i>
                开始阅读
            </a>
            <a href="/about" class="btn btn-secondary">
                <i class="fas fa-info-circle"></i>
                了解更多
            </a>
        </div>
    </div>
    <div class="hero-scroll-indicator">
        <i class="fas fa-chevron-down"></i>
    </div>
</section>

<!-- 统计数据区域 -->
<% if (stats && Object.keys(stats).length > 0) { %>
<section class="stats-section">
    <div class="container">
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><%= stats.totalArticles || 0 %></div>
                    <div class="stat-label">文章总数</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><%= stats.totalCategories || 0 %></div>
                    <div class="stat-label">分类数量</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><%= stats.totalComments || 0 %></div>
                    <div class="stat-label">评论总数</div>
                </div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">
                    <i class="fas fa-edit"></i>
                </div>
                <div class="stat-content">
                    <div class="stat-number"><%= stats.totalDrafts || 0 %></div>
                    <div class="stat-label">草稿数量</div>
                </div>
            </div>
        </div>
    </div>
</section>
<% } %>

<!-- 最新文章区域 -->
<section class="featured-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">最新文章</h2>
            <p class="section-subtitle">最新发布的精彩内容</p>
        </div>
        
        <% if (articles && articles.length > 0) { %>
            <div class="articles-grid">
                <% articles.forEach(article => { %>
                    <article class="article-card">
                        <a href="/articles/<%= article._id %>" class="article-link">
                            <h3 class="article-title"><%= article.title %></h3>
                            <div class="article-meta">
                                <span class="article-author">
                                    <i class="fas fa-user"></i>
                                    <%= article.author ? article.author.username : '匿名' %>
                                </span>
                                <span class="article-date">
                                    <i class="fas fa-calendar"></i>
                                    <%= new Date(article.createdAt).toLocaleDateString('zh-CN') %>
                                </span>
                                <% if (article.category) { %>
                                <span class="article-category">
                                    <i class="fas fa-folder"></i>
                                    <%= article.category.name %>
                                </span>
                                <% } %>
                            </div>
                            <div class="article-excerpt">
                                <%= article.content.substring(0, 150) %>...
                            </div>
                            <% if (article.tags && article.tags.length > 0) { %>
                            <div class="article-tags">
                                <% article.tags.forEach(tag => { %>
                                    <span class="tag">#<%= tag %></span>
                                <% }) %>
                            </div>
                            <% } %>
                            <div class="article-footer">
                                <span class="article-views">
                                    <i class="fas fa-eye"></i>
                                    <%= article.viewCount || 0 %> 次浏览
                                </span>
                                <span class="read-more">
                                    阅读更多
                                    <i class="fas fa-arrow-right"></i>
                                </span>
                            </div>
                        </a>
                    </article>
                <% }) %>
            </div>
            
            <div class="section-footer">
                <a href="/articles" class="btn btn-outline">
                    <i class="fas fa-list"></i>
                    查看所有文章
                </a>
            </div>
        <% } else { %>
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>暂无文章</h3>
                <p>还没有发布任何文章，请稍后再来查看。</p>
            </div>
        <% } %>
    </div>
</section>

<!-- 热门文章侧边栏 -->
<% if (popularArticles && popularArticles.length > 0) { %>
<section class="popular-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">热门文章</h2>
            <p class="section-subtitle">最受欢迎的内容</p>
        </div>
        
        <div class="popular-grid">
            <% popularArticles.forEach((article, index) => { %>
                <div class="popular-item">
                    <div class="popular-rank">#<%= index + 1 %></div>
                    <div class="popular-content">
                        <h4 class="popular-title">
                            <a href="/articles/<%= article._id %>"><%= article.title %></a>
                        </h4>
                        <div class="popular-meta">
                            <span class="popular-views">
                                <i class="fas fa-eye"></i>
                                <%= article.viewCount || 0 %>
                            </span>
                            <span class="popular-date">
                                <%= new Date(article.createdAt).toLocaleDateString('zh-CN') %>
                            </span>
                        </div>
                    </div>
                </div>
            <% }) %>
        </div>
    </div>
</section>
<% } %>

<!-- 分类展示区域 -->
<% if (categories && categories.length > 0) { %>
<section class="categories-section">
    <div class="container">
        <div class="section-header">
            <h2 class="section-title">文章分类</h2>
            <p class="section-subtitle">按主题浏览内容</p>
        </div>
        
        <div class="categories-grid">
            <% categories.forEach(category => { %>
                <div class="category-card">
                    <a href="/articles/category/<%= category._id %>" class="category-link">
                        <div class="category-icon">
                            <i class="fas fa-folder"></i>
                        </div>
                        <h3 class="category-name"><%= category.name %></h3>
                        <% if (category.description) { %>
                        <p class="category-description"><%= category.description %></p>
                        <% } %>
                        <div class="category-count">
                            <i class="fas fa-file-alt"></i>
                            文章数量
                        </div>
                    </a>
                </div>
            <% }) %>
        </div>
        
        <div class="section-footer">
            <a href="/categories" class="btn btn-outline">
                <i class="fas fa-th-large"></i>
                查看所有分类
            </a>
        </div>
    </div>
</section>
<% } %>

<%- include('partials/footer') %>
