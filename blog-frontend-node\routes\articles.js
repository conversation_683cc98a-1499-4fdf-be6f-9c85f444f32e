const express = require('express');
const axios = require('axios');
const router = express.Router();

const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000/api';

// 创建axios实例
const api = axios.create({
    baseURL: API_BASE_URL,
    timeout: 10000
});

// 文章列表页面
router.get('/', async (req, res) => {
    try {
        const { category, tags, page = 1, limit = 10 } = req.query;
        
        // 获取文章列表
        const articlesRes = await api.get('/articles', {
            params: { category, tags, page, limit }
        });

        // 获取分类列表
        const categoriesRes = await api.get('/categories');
        
        const articles = articlesRes.data.data || [];
        const pagination = articlesRes.data.pagination || {};
        const categories = categoriesRes.data.data || [];

        res.render('articles', {
            title: '文章列表',
            articles,
            categories,
            tags: [], // 添加空的tags数组
            pagination,
            currentCategory: category || '',
            currentTags: tags || '',
            currentPage: 'articles'
        });
    } catch (error) {
        console.error('文章列表加载错误:', error.message);
        res.render('articles', {
            title: '文章列表',
            articles: [],
            categories: [],
            tags: [], // 添加空的tags数组
            pagination: {},
            currentCategory: '',
            currentTags: '',
            currentPage: 'articles',
            error: '文章加载失败，请稍后重试'
        });
    }
});

// 文章详情页面
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        
        // 并行获取文章详情和评论
        const [articleRes, commentsRes] = await Promise.allSettled([
            api.get(`/articles/${id}`),
            api.get(`/articles/${id}/comments`)
        ]);

        if (articleRes.status === 'rejected') {
            return res.status(404).render('error', {
                title: '文章未找到',
                error: {
                    status: 404,
                    message: '抱歉，您访问的文章不存在'
                }
            });
        }

        const article = articleRes.value.data.data;
        const comments = commentsRes.status === 'fulfilled' ? commentsRes.value.data.data : [];

        // 获取相关文章（基于标签或分类）
        let relatedArticles = [];
        try {
            if (article.tags && article.tags.length > 0) {
                const relatedRes = await api.get('/articles', {
                    params: { 
                        tags: article.tags.join(','), 
                        limit: 4 
                    }
                });
                relatedArticles = relatedRes.data.data.filter(a => a._id !== article._id);
            }
        } catch (err) {
            console.error('获取相关文章失败:', err.message);
        }

        res.render('article-detail', {
            title: article.title,
            article,
            comments,
            relatedArticles,
            currentPage: 'articles'
        });
    } catch (error) {
        console.error('文章详情加载错误:', error.message);
        res.status(500).render('error', {
            title: '加载错误',
            error: {
                status: 500,
                message: '文章加载失败，请稍后重试'
            }
        });
    }
});

// 按分类获取文章
router.get('/category/:categoryId', async (req, res) => {
    try {
        const { categoryId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        
        // 获取分类信息
        const categoryRes = await api.get(`/categories/${categoryId}`);
        const category = categoryRes.data.data;
        
        // 获取该分类下的文章
        const articlesRes = await api.get('/articles', {
            params: { category: categoryId, page, limit }
        });
        
        const articles = articlesRes.data.data || [];
        const pagination = articlesRes.data.pagination || {};

        res.render('articles/category', {
            title: `${category.name} - 分类文章`,
            category,
            articles,
            pagination,
            currentPage: 'articles'
        });
    } catch (error) {
        console.error('分类文章加载错误:', error.message);
        res.status(404).render('error', {
            title: '分类未找到',
            error: {
                status: 404,
                message: '抱歉，您访问的分类不存在'
            }
        });
    }
});

// 按标签获取文章
router.get('/tag/:tag', async (req, res) => {
    try {
        const { tag } = req.params;
        const { page = 1, limit = 10 } = req.query;
        
        // 获取该标签下的文章
        const articlesRes = await api.get('/articles', {
            params: { tags: tag, page, limit }
        });
        
        const articles = articlesRes.data.data || [];
        const pagination = articlesRes.data.pagination || {};

        res.render('articles/tag', {
            title: `${tag} - 标签文章`,
            tag,
            articles,
            pagination,
            currentPage: 'articles'
        });
    } catch (error) {
        console.error('标签文章加载错误:', error.message);
        res.render('articles/tag', {
            title: `${req.params.tag} - 标签文章`,
            tag: req.params.tag,
            articles: [],
            pagination: {},
            currentPage: 'articles',
            error: '文章加载失败，请稍后重试'
        });
    }
});

module.exports = router;
