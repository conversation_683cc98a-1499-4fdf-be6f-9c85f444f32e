/* ========== 基础样式和变量 ========== */
:root {
    /* iOS风格颜色系统 */
    --primary-color: #007AFF;
    --primary-dark: #0056CC;
    --secondary-color: #5856D6;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --error-color: #FF3B30;
    
    /* 中性色系 */
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --text-tertiary: #C7C7CC;
    --background-primary: #FFFFFF;
    --background-secondary: #F2F2F7;
    --background-tertiary: #FFFFFF;
    --surface-color: rgba(255, 255, 255, 0.8);
    --overlay-color: rgba(0, 0, 0, 0.4);
    
    /* 阴影系统 */
    --shadow-small: 0 2px 8px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
    --shadow-large: 0 8px 32px rgba(0, 0, 0, 0.15);
    
    /* 圆角系统 */
    --radius-small: 8px;
    --radius-medium: 12px;
    --radius-large: 16px;
    --radius-xl: 24px;
    
    /* 间距系统 */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-2xl: 48px;
    --spacing-3xl: 64px;
    
    /* 字体系统 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 12px;
    --font-size-sm: 14px;
    --font-size-base: 16px;
    --font-size-lg: 18px;
    --font-size-xl: 20px;
    --font-size-2xl: 24px;
    --font-size-3xl: 32px;
    --font-size-4xl: 40px;
    
    /* 动画 */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.25s ease-out;
    --transition-slow: 0.35s ease-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #FFFFFF;
        --text-secondary: #98989D;
        --text-tertiary: #636366;
        --background-primary: #000000;
        --background-secondary: #1C1C1E;
        --background-tertiary: #2C2C2E;
        --surface-color: rgba(28, 28, 30, 0.8);
        --overlay-color: rgba(0, 0, 0, 0.6);
    }
}

/* ========== 基础重置 ========== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    padding-top: 80px; /* 为固定导航栏留出空间 */
}

/* ========== 通用组件 ========== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-md);
    }
}

/* 按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--radius-large);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-small);
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-outline {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-secondary {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--text-tertiary);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

.btn-secondary:hover {
    background: var(--background-tertiary);
    border-color: var(--text-secondary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-small);
}

/* 卡片组件 */
.card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

/* Alert组件 */
.alert {
    padding: var(--spacing-md) var(--spacing-lg);
    border-radius: var(--radius-medium);
    margin: var(--spacing-md) 0;
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-weight: 500;
}

.alert-success {
    background: rgba(52, 199, 89, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(52, 199, 89, 0.2);
}

.alert-error {
    background: rgba(255, 59, 48, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(255, 59, 48, 0.2);
}

.alert-warning {
    background: rgba(255, 149, 0, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(255, 149, 0, 0.2);
}

/* 模糊效果 */
.blur-background {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* ========== 导航栏 ========== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: var(--surface-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand .brand-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-medium);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background: rgba(0, 122, 255, 0.1);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.search-container {
    position: relative;
}

.search-form {
    display: flex;
    align-items: center;
}

.search-input {
    padding: var(--spacing-sm) var(--spacing-md);
    border: 1px solid var(--text-tertiary);
    border-radius: var(--radius-medium);
    background: var(--background-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    width: 200px;
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.search-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: var(--spacing-sm);
    border-radius: var(--radius-medium);
    margin-left: var(--spacing-xs);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.search-btn:hover {
    background: var(--primary-dark);
}

.auth-buttons {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.user-menu {
    position: relative;
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--background-secondary);
    border-radius: var(--radius-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.user-avatar:hover {
    background: var(--primary-color);
    color: white;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--surface-color);
    border-radius: var(--radius-medium);
    box-shadow: var(--shadow-medium);
    padding: var(--spacing-sm);
    min-width: 150px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.user-menu:hover .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    color: var(--text-primary);
    text-decoration: none;
    border-radius: var(--radius-small);
    transition: all var(--transition-fast);
    width: 100%;
    border: none;
    background: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.dropdown-item:hover {
    background: var(--background-secondary);
}

.logout-btn {
    color: var(--error-color);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-sm);
}

.nav-toggle span {
    width: 20px;
    height: 2px;
    background: var(--text-primary);
    border-radius: 1px;
    transition: all var(--transition-fast);
}

/* 移动端导航 */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--surface-color);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        flex-direction: column;
        padding: var(--spacing-lg);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .search-input {
        width: 150px;
    }

    .auth-buttons {
        flex-direction: column;
        gap: var(--spacing-xs);
    }
}

/* ========== 主要内容区域 ========== */
.main-content {
    min-height: calc(100vh - 80px);
    padding: var(--spacing-2xl) 0;
}

/* ========== 英雄区域 ========== */
.hero-section {
    position: relative;
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    margin-bottom: var(--spacing-3xl);
    border-radius: var(--radius-xl);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="50" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="30" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.2);
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
    padding: 0 var(--spacing-lg);
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    line-height: 1.2;
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-2xl);
    opacity: 0.9;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.hero-scroll-indicator {
    position: absolute;
    bottom: var(--spacing-lg);
    left: 50%;
    transform: translateX(-50%);
    color: white;
    opacity: 0.7;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateX(-50%) translateY(0);
    }
    40% {
        transform: translateX(-50%) translateY(-10px);
    }
    60% {
        transform: translateX(-50%) translateY(-5px);
    }
}

@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-3xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}

/* ========== 文章卡片 ========== */
.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-2xl);
}

.article-card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
    text-decoration: none;
    color: inherit;
}

.article-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    text-decoration: none;
    color: inherit;
}

.article-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
    line-height: 1.3;
}

.article-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.article-excerpt {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
}

.tag {
    background: rgba(0, 122, 255, 0.1);
    color: var(--primary-color);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-small);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-decoration: none;
    transition: all var(--transition-fast);
}

.tag:hover {
    background: var(--primary-color);
    color: white;
}

.article-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-md);
    border-top: 1px solid var(--text-tertiary);
}

.read-more {
    color: var(--primary-color);
    font-weight: 500;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    transition: all var(--transition-fast);
}

.read-more:hover {
    gap: var(--spacing-sm);
}

/* ========== 分页 ========== */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-sm);
    margin: var(--spacing-2xl) 0;
}

.pagination a,
.pagination span {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-medium);
    text-decoration: none;
    color: var(--text-secondary);
    transition: all var(--transition-fast);
}

.pagination a:hover {
    background: var(--primary-color);
    color: white;
}

.pagination .current {
    background: var(--primary-color);
    color: white;
}

/* ========== 页脚 ========== */
.footer {
    background: var(--background-secondary);
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
    margin-top: var(--spacing-3xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-color);
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-color);
}

.footer-social {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: var(--surface-color);
    color: var(--text-secondary);
    border-radius: var(--radius-medium);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.social-link:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--spacing-lg);
    border-top: 1px solid var(--text-tertiary);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* ========== 统计卡片 ========== */
.stats-section {
    margin: var(--spacing-3xl) 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
}

.stat-card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: white;
    font-size: var(--font-size-xl);
}

.stat-number {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* ========== 区域标题 ========== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.section-footer {
    text-align: center;
    margin-top: var(--spacing-2xl);
}

/* ========== 特色区域 ========== */
.featured-section {
    margin: var(--spacing-3xl) 0;
}

/* ========== 热门文章 ========== */
.popular-section {
    margin: var(--spacing-3xl) 0;
    background: var(--background-secondary);
    padding: var(--spacing-3xl) 0;
    border-radius: var(--radius-xl);
}

.popular-grid {
    display: grid;
    gap: var(--spacing-lg);
    max-width: 800px;
    margin: 0 auto;
}

.popular-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    background: var(--surface-color);
    padding: var(--spacing-lg);
    border-radius: var(--radius-large);
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.popular-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.popular-rank {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    color: white;
    border-radius: var(--radius-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--font-size-lg);
    flex-shrink: 0;
}

.popular-content {
    flex: 1;
}

.popular-title {
    margin-bottom: var(--spacing-sm);
}

.popular-title a {
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-fast);
}

.popular-title a:hover {
    color: var(--primary-color);
}

.popular-meta {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* ========== 分类卡片 ========== */
.categories-section {
    margin: var(--spacing-3xl) 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
}

.category-card {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-lg);
    text-align: center;
    box-shadow: var(--shadow-small);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all var(--transition-normal);
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.category-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-large);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-md);
    color: white;
    font-size: var(--font-size-xl);
}

.category-name {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.category-description {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
    line-height: 1.5;
}

.category-count {
    color: var(--primary-color);
    font-size: var(--font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs);
}

/* ========== 空状态 ========== */
.empty-state {
    text-align: center;
    padding: var(--spacing-3xl);
    color: var(--text-secondary);
}

.empty-state i {
    font-size: var(--font-size-4xl);
    margin-bottom: var(--spacing-lg);
    opacity: 0.5;
}

.empty-state h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.empty-state p {
    font-size: var(--font-size-base);
    max-width: 400px;
    margin: 0 auto;
}

/* ========== 文章链接样式 ========== */
.article-link {
    text-decoration: none;
    color: inherit;
    display: block;
}

.article-link:hover {
    text-decoration: none;
    color: inherit;
}

/* ========== 响应式设计 ========== */
@media (max-width: 768px) {
    .articles-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--spacing-md);
    }

    .categories-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .popular-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .popular-meta {
        justify-content: center;
    }

    .section-title {
        font-size: var(--font-size-2xl);
    }

    .section-subtitle {
        font-size: var(--font-size-base);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .hero-section {
        min-height: 50vh;
        margin-bottom: var(--spacing-2xl);
    }

    .main-content {
        padding: var(--spacing-lg) 0;
    }
}
