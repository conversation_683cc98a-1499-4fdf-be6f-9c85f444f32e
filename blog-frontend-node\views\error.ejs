<%- include('partials/header') %>

<div class="error-page">
    <div class="container">
        <div class="error-content">
            <div class="error-icon">
                <% if (typeof status !== 'undefined' && status === 404) { %>
                    <i class="fas fa-search"></i>
                <% } else if (typeof status !== 'undefined' && status === 500) { %>
                    <i class="fas fa-exclamation-triangle"></i>
                <% } else { %>
                    <i class="fas fa-bug"></i>
                <% } %>
            </div>
            
            <div class="error-code">
                <%= typeof status !== 'undefined' ? status : '错误' %>
            </div>
            
            <h1 class="error-title">
                <% if (typeof status !== 'undefined' && status === 404) { %>
                    页面未找到
                <% } else if (typeof status !== 'undefined' && status === 500) { %>
                    服务器内部错误
                <% } else { %>
                    出现了一些问题
                <% } %>
            </h1>
            
            <p class="error-message">
                <% if (typeof message !== 'undefined' && message) { %>
                    <%= message %>
                <% } else if (typeof status !== 'undefined' && status === 404) { %>
                    抱歉，您访问的页面不存在或已被移除。
                <% } else if (typeof status !== 'undefined' && status === 500) { %>
                    服务器遇到了一些问题，请稍后再试。
                <% } else { %>
                    系统遇到了一些问题，我们正在努力修复。
                <% } %>
            </p>
            
            <div class="error-actions">
                <a href="/" class="btn btn-primary">
                    <i class="fas fa-home"></i>
                    返回首页
                </a>
                <button onclick="history.back()" class="btn btn-outline">
                    <i class="fas fa-arrow-left"></i>
                    返回上页
                </button>
                <button onclick="location.reload()" class="btn btn-outline">
                    <i class="fas fa-redo"></i>
                    刷新页面
                </button>
            </div>
            
            <div class="error-help">
                <h3>需要帮助？</h3>
                <ul class="help-list">
                    <li>检查网址是否正确</li>
                    <li>尝试刷新页面</li>
                    <li>返回首页重新导航</li>
                    <li>如果问题持续存在，请联系我们</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<%- include('partials/footer') %>

<style>
.error-page {
    min-height: 70vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-2xl) 0;
}

.error-content {
    text-align: center;
    max-width: 600px;
    margin: 0 auto;
}

.error-icon {
    font-size: 6rem;
    color: var(--primary-color);
    margin-bottom: var(--spacing-lg);
    opacity: 0.8;
}

.error-code {
    font-size: 8rem;
    font-weight: 700;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: var(--spacing-md);
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.error-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.error-message {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-2xl);
    line-height: 1.6;
}

.error-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-2xl);
    flex-wrap: wrap;
}

.error-help {
    background: var(--surface-color);
    border-radius: var(--radius-large);
    padding: var(--spacing-xl);
    margin-top: var(--spacing-2xl);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.error-help h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
}

.help-list {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: left;
}

.help-list li {
    padding: var(--spacing-sm) 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: var(--spacing-lg);
}

.help-list li::before {
    content: '•';
    color: var(--primary-color);
    position: absolute;
    left: 0;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .error-code {
        font-size: 6rem;
    }
    
    .error-title {
        font-size: var(--font-size-2xl);
    }
    
    .error-message {
        font-size: var(--font-size-base);
    }
    
    .error-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .error-actions .btn {
        width: 200px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-content > * {
    animation: fadeInUp 0.6s ease-out forwards;
}

.error-content > *:nth-child(2) {
    animation-delay: 0.1s;
}

.error-content > *:nth-child(3) {
    animation-delay: 0.2s;
}

.error-content > *:nth-child(4) {
    animation-delay: 0.3s;
}

.error-content > *:nth-child(5) {
    animation-delay: 0.4s;
}

.error-content > *:nth-child(6) {
    animation-delay: 0.5s;
}
</style>
