<%- include('partials/header', { title: '用户登录', currentPage: 'login' }) %>

<div class="auth-page">
    <div class="container">
        <div class="auth-container">
            <div class="auth-card">
                <div class="auth-header">
                    <h1 class="auth-title">欢迎回来</h1>
                    <p class="auth-subtitle">登录您的账号</p>
                </div>
                
                <form class="auth-form" action="/auth/login" method="POST">
                    <div class="form-group">
                        <label for="email" class="form-label">邮箱地址</label>
                        <input
                            type="email"
                            id="email"
                            name="email"
                            class="form-input"
                            placeholder="请输入邮箱地址"
                            required
                        >
                    </div>
                    
                    <div class="form-group">
                        <label for="password" class="form-label">密码</label>
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-input" 
                            placeholder="请输入密码"
                            required
                        >
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-full">
                        <i class="fas fa-sign-in-alt"></i>
                        登录
                    </button>
                </form>
                
                <div class="auth-footer">
                    <p>还没有账号？ <a href="/auth/register" class="auth-link">立即注册</a></p>
                </div>
            </div>
        </div>
    </div>
</div>

<%- include('partials/footer') %>

<style>
.auth-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: var(--spacing-xl) 0;
}

.auth-container {
    width: 100%;
    max-width: 400px;
    margin: 0 auto;
}

.auth-card {
    background: var(--surface-color);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--radius-xl);
    padding: var(--spacing-3xl);
    box-shadow: var(--shadow-large);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.auth-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.auth-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.auth-subtitle {
    color: var(--text-secondary);
    font-size: var(--font-size-base);
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.form-label {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

.form-input {
    padding: var(--spacing-md);
    border: 1px solid var(--text-tertiary);
    border-radius: var(--radius-medium);
    background: var(--background-secondary);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: all var(--transition-fast);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.btn-full {
    width: 100%;
    justify-content: center;
    padding: var(--spacing-lg);
    font-size: var(--font-size-base);
    margin-top: var(--spacing-md);
}

.auth-footer {
    text-align: center;
    margin-top: var(--spacing-xl);
    padding-top: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-footer p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.auth-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-fast);
}

.auth-link:hover {
    color: var(--primary-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-card {
        padding: var(--spacing-2xl);
        margin: var(--spacing-lg);
    }
    
    .auth-title {
        font-size: var(--font-size-xl);
    }
}

/* 动画效果 */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.auth-card {
    animation: slideInUp 0.6s ease-out;
}
</style>
